[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bo<PERSON> #5 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #5 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #33 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #33 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #41 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #41 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #4 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #4 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #2 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #2 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #3 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #3 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #1 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_START: Bot #1 starting sequential processing for Channel: -1002861074047, Message: 61
[2025-08-07 00:03:11] QUEUE_CREATE_START: Bot #1 creating queue for Channel: -1002861074047, Message: 61
[2025-08-07 00:03:11] QUEUE_CREATE_SUCCESS: Added 43 reactions for Channel: -1002861074047, Message: 61, Duration: 860s
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #34 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #34 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #37 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #37 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #15 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #15 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #42 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #42 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #9 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #9 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #36 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #36 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #8 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #8 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #10 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #10 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #11 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #11 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #35 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #35 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #14 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #14 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #39 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #39 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #12 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #12 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #13 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #13 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #16 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #16 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #44 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #44 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #6 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #6 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #17 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #17 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #40 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #40 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #38 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #38 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:11] WEBHOOK_RECEIVED: Bot #7 received message from Channel: -1002861074047, Message: 61, Timestamp: 2025-08-07 00:03:11
[2025-08-07 00:03:11] SEQUENTIAL_SKIP: Bot #7 skipping immediate reaction for Channel: -1002861074047, Message: 61 (will process via queue)
[2025-08-07 00:03:12] SEQUENTIAL_BOT1_SUCCESS: Bot #1 → '🎉' (Channel: -1002861074047, Message: 61) - IMMEDIATE
[2025-08-07 00:04:06] QUEUE_SUCCESS: Bot #2 → '❤️' (Channel: -1002861074047, Message: 61, Delay: 54s)
[2025-08-07 00:04:06] QUEUE_SUCCESS: Bot #3 → '🎉' (Channel: -1002861074047, Message: 61, Delay: 54s)
[2025-08-07 00:04:06] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 41 remaining
[2025-08-07 00:04:06] POST_STATS: -1002861074047_61 - 2 successful, 0 errors
[2025-08-07 00:05:04] QUEUE_SUCCESS: Bot #4 → '❤️' (Channel: -1002861074047, Message: 61, Delay: 112s)
[2025-08-07 00:05:05] QUEUE_SUCCESS: Bot #5 → '🥰' (Channel: -1002861074047, Message: 61, Delay: 112s)
[2025-08-07 00:05:06] QUEUE_SUCCESS: Bot #6 → '🤩' (Channel: -1002861074047, Message: 61, Delay: 112s)
[2025-08-07 00:05:06] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 38 remaining
[2025-08-07 00:05:06] POST_STATS: -1002861074047_61 - 3 successful, 0 errors
[2025-08-07 00:06:05] QUEUE_SUCCESS: Bot #7 → '😘' (Channel: -1002861074047, Message: 61, Delay: 173s)
[2025-08-07 00:06:06] QUEUE_SUCCESS: Bot #8 → '💯' (Channel: -1002861074047, Message: 61, Delay: 173s)
[2025-08-07 00:06:07] QUEUE_SUCCESS: Bot #9 → '🥰' (Channel: -1002861074047, Message: 61, Delay: 173s)
[2025-08-07 00:06:07] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 35 remaining
[2025-08-07 00:06:07] POST_STATS: -1002861074047_61 - 3 successful, 0 errors
[2025-08-07 00:07:04] QUEUE_SUCCESS: Bot #10 → '❤️' (Channel: -1002861074047, Message: 61, Delay: 232s)
[2025-08-07 00:07:05] QUEUE_SUCCESS: Bot #11 → '🤩' (Channel: -1002861074047, Message: 61, Delay: 232s)
[2025-08-07 00:07:06] QUEUE_SUCCESS: Bot #12 → '😘' (Channel: -1002861074047, Message: 61, Delay: 232s)
[2025-08-07 00:07:06] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 32 remaining
[2025-08-07 00:07:06] POST_STATS: -1002861074047_61 - 3 successful, 0 errors
[2025-08-07 00:08:05] QUEUE_SUCCESS: Bot #13 → '🥰' (Channel: -1002861074047, Message: 61, Delay: 293s)
[2025-08-07 00:08:05] QUEUE_SUCCESS: Bot #14 → '🤩' (Channel: -1002861074047, Message: 61, Delay: 293s)
[2025-08-07 00:08:06] QUEUE_SUCCESS: Bot #15 → '😘' (Channel: -1002861074047, Message: 61, Delay: 293s)
[2025-08-07 00:08:06] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 29 remaining
[2025-08-07 00:08:06] POST_STATS: -1002861074047_61 - 3 successful, 0 errors
[2025-08-07 00:09:04] QUEUE_SUCCESS: Bot #16 → '🤩' (Channel: -1002861074047, Message: 61, Delay: 352s)
[2025-08-07 00:09:05] QUEUE_SUCCESS: Bot #17 → '❤️' (Channel: -1002861074047, Message: 61, Delay: 352s)
[2025-08-07 00:09:06] QUEUE_ERROR: Bot #18 → '❤️' (Channel: -1002861074047, Message: 61) - HTTP 400: Bad Request: chat not found
[2025-08-07 00:09:06] QUEUE_SUMMARY: Processed 2 reactions, 1 errors, 26 remaining
[2025-08-07 00:09:06] POST_STATS: -1002861074047_61 - 2 successful, 1 errors
[2025-08-07 00:10:05] QUEUE_ERROR: Bot #19 → '🥰' (Channel: -1002861074047, Message: 61) - HTTP 400: Bad Request: chat not found
[2025-08-07 00:10:06] QUEUE_ERROR: Bot #20 → '🎉' (Channel: -1002861074047, Message: 61) - HTTP 400: Bad Request: chat not found
[2025-08-07 00:10:07] QUEUE_ERROR: Bot #21 → '🎉' (Channel: -1002861074047, Message: 61) - HTTP 400: Bad Request: chat not found
[2025-08-07 00:10:07] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 23 remaining
[2025-08-07 00:10:07] POST_STATS: -1002861074047_61 - 0 successful, 3 errors
[2025-08-07 00:11:04] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002861074047, Message: 61) - HTTP 400: Bad Request: chat not found
[2025-08-07 00:11:05] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002861074047, Message: 61) - HTTP 400: Bad Request: chat not found
[2025-08-07 00:11:06] QUEUE_ERROR: Bot #24 → '💯' (Channel: -1002861074047, Message: 61) - HTTP 400: Bad Request: chat not found
[2025-08-07 00:11:06] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 20 remaining
[2025-08-07 00:11:06] POST_STATS: -1002861074047_61 - 0 successful, 3 errors
[2025-08-07 00:12:05] QUEUE_ERROR: Bot #25 → '💯' (Channel: -1002861074047, Message: 61) - HTTP 400: Bad Request: chat not found
[2025-08-07 00:12:06] QUEUE_ERROR: Bot #26 → '🥰' (Channel: -1002861074047, Message: 61) - HTTP 400: Bad Request: chat not found
[2025-08-07 00:12:06] QUEUE_ERROR: Bot #27 → '🏆' (Channel: -1002861074047, Message: 61) - HTTP 400: Bad Request: chat not found
[2025-08-07 00:12:06] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 17 remaining
[2025-08-07 00:12:06] POST_STATS: -1002861074047_61 - 0 successful, 3 errors
[2025-08-07 00:13:05] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002861074047, Message: 61) - HTTP 401: Unauthorized
[2025-08-07 00:13:06] QUEUE_ERROR: Bot #29 → '🎉' (Channel: -1002861074047, Message: 61) - HTTP 400: Bad Request: chat not found
[2025-08-07 00:13:07] QUEUE_ERROR: Bot #30 → '❤️' (Channel: -1002861074047, Message: 61) - HTTP 400: Bad Request: chat not found
[2025-08-07 00:13:07] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 14 remaining
[2025-08-07 00:13:07] POST_STATS: -1002861074047_61 - 0 successful, 3 errors
[2025-08-07 00:14:05] QUEUE_ERROR: Bot #31 → '🥰' (Channel: -1002861074047, Message: 61) - HTTP 400: Bad Request: chat not found
[2025-08-07 00:14:06] QUEUE_ERROR: Bot #32 → '🏆' (Channel: -1002861074047, Message: 61) - HTTP 400: Bad Request: chat not found
[2025-08-07 00:14:07] QUEUE_SUCCESS: Bot #33 → '🎉' (Channel: -1002861074047, Message: 61, Delay: 653s)
[2025-08-07 00:14:07] QUEUE_SUMMARY: Processed 1 reactions, 2 errors, 11 remaining
[2025-08-07 00:14:07] POST_STATS: -1002861074047_61 - 1 successful, 2 errors
[2025-08-07 00:15:05] QUEUE_SUCCESS: Bot #34 → '😘' (Channel: -1002861074047, Message: 61, Delay: 713s)
[2025-08-07 00:15:06] QUEUE_SUCCESS: Bot #35 → '🤩' (Channel: -1002861074047, Message: 61, Delay: 713s)
[2025-08-07 00:15:07] QUEUE_SUCCESS: Bot #36 → '❤️' (Channel: -1002861074047, Message: 61, Delay: 713s)
[2025-08-07 00:15:07] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 8 remaining
[2025-08-07 00:15:07] POST_STATS: -1002861074047_61 - 3 successful, 0 errors
[2025-08-07 00:16:05] QUEUE_SUCCESS: Bot #37 → '❤️' (Channel: -1002861074047, Message: 61, Delay: 773s)
[2025-08-07 00:16:06] QUEUE_SUCCESS: Bot #38 → '😘' (Channel: -1002861074047, Message: 61, Delay: 773s)
[2025-08-07 00:16:06] QUEUE_SUCCESS: Bot #39 → '💯' (Channel: -1002861074047, Message: 61, Delay: 773s)
[2025-08-07 00:16:06] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 5 remaining
[2025-08-07 00:16:06] POST_STATS: -1002861074047_61 - 3 successful, 0 errors
[2025-08-07 00:17:04] QUEUE_SUCCESS: Bot #40 → '🏆' (Channel: -1002861074047, Message: 61, Delay: 832s)
[2025-08-07 00:17:05] QUEUE_SUCCESS: Bot #41 → '🤩' (Channel: -1002861074047, Message: 61, Delay: 832s)
[2025-08-07 00:17:06] QUEUE_SUCCESS: Bot #42 → '😘' (Channel: -1002861074047, Message: 61, Delay: 832s)
[2025-08-07 00:17:06] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 2 remaining
[2025-08-07 00:17:06] POST_STATS: -1002861074047_61 - 3 successful, 0 errors
[2025-08-07 00:18:06] QUEUE_ERROR: Bot #43 → '💯' (Channel: -1002861074047, Message: 61) - HTTP 401: Unauthorized
[2025-08-07 00:18:07] QUEUE_SUCCESS: Bot #44 → '😘' (Channel: -1002861074047, Message: 61, Delay: 892s)
[2025-08-07 00:18:07] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 0 remaining
[2025-08-07 00:18:07] POST_STATS: -1002861074047_61 - 1 successful, 1 errors
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #1 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_START: Bot #1 starting sequential processing for Channel: -1002888471861, Message: 264
[2025-08-07 06:16:16] QUEUE_CREATE_START: Bot #1 creating queue for Channel: -1002888471861, Message: 264
[2025-08-07 06:16:16] QUEUE_CREATE_SUCCESS: Added 43 reactions for Channel: -1002888471861, Message: 264, Duration: 860s
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #34 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #34 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #4 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #4 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #3 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #3 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #2 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #2 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #5 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #5 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #41 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #41 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #19 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #19 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #21 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #21 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #33 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #33 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #30 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #30 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #23 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #23 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #25 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #25 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #20 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #20 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #24 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #24 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #10 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #10 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #13 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #13 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #8 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #8 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #12 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #12 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #15 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #15 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #16 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #16 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #35 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #35 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #37 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #37 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #6 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #6 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #38 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #38 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #39 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #39 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #44 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #44 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #11 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #11 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #40 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #40 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #14 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #14 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #17 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #17 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #42 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #42 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #7 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #7 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #36 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #36 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:16] WEBHOOK_RECEIVED: Bot #9 received message from Channel: -1002888471861, Message: 264, Timestamp: 2025-08-07 06:16:16
[2025-08-07 06:16:16] SEQUENTIAL_SKIP: Bot #9 skipping immediate reaction for Channel: -1002888471861, Message: 264 (will process via queue)
[2025-08-07 06:16:17] SEQUENTIAL_BOT1_SUCCESS: Bot #1 → '🤩' (Channel: -1002888471861, Message: 264) - IMMEDIATE
[2025-08-07 06:17:01] QUEUE_SUCCESS: Bot #2 → '💯' (Channel: -1002888471861, Message: 264, Delay: 44s)
[2025-08-07 06:17:01] QUEUE_SUCCESS: Bot #2 → '💯' (Channel: -1002888471861, Message: 264, Delay: 44s)
[2025-08-07 06:17:01] QUEUE_SUCCESS: Bot #2 → '💯' (Channel: -1002888471861, Message: 264, Delay: 44s)
[2025-08-07 06:17:01] QUEUE_SUCCESS: Bot #2 → '💯' (Channel: -1002888471861, Message: 264, Delay: 44s)
[2025-08-07 06:17:01] QUEUE_SUCCESS: Bot #2 → '💯' (Channel: -1002888471861, Message: 264, Delay: 44s)
[2025-08-07 06:17:01] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 264, Delay: 44s)
[2025-08-07 06:17:01] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 41 remaining
[2025-08-07 06:17:01] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 264, Delay: 44s)
[2025-08-07 06:17:01] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 41 remaining
[2025-08-07 06:17:01] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 264, Delay: 44s)
[2025-08-07 06:17:01] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 41 remaining
[2025-08-07 06:17:01] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 264, Delay: 44s)
[2025-08-07 06:17:01] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 41 remaining
[2025-08-07 06:17:02] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 264, Delay: 44s)
[2025-08-07 06:17:02] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 41 remaining
[2025-08-07 06:17:06] QUEUE_SUCCESS: Bot #2 → '💯' (Channel: -1002888471861, Message: 264, Delay: 44s)
[2025-08-07 06:17:06] QUEUE_SUCCESS: Bot #2 → '💯' (Channel: -1002888471861, Message: 264, Delay: 44s)
[2025-08-07 06:17:06] QUEUE_SUCCESS: Bot #2 → '💯' (Channel: -1002888471861, Message: 264, Delay: 44s)
[2025-08-07 06:17:06] QUEUE_SUCCESS: Bot #2 → '💯' (Channel: -1002888471861, Message: 264, Delay: 44s)
[2025-08-07 06:17:06] QUEUE_ERROR: Bot #2 → '💯' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:06] QUEUE_ERROR: Bot #2 → '💯' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:06] QUEUE_ERROR: Bot #2 → '💯' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:06] QUEUE_ERROR: Bot #2 → '💯' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:06] QUEUE_ERROR: Bot #2 → '💯' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:06] QUEUE_ERROR: Bot #2 → '💯' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:06] QUEUE_ERROR: Bot #2 → '💯' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:06] QUEUE_SUCCESS: Bot #2 → '💯' (Channel: -1002888471861, Message: 264, Delay: 44s)
[2025-08-07 06:17:06] QUEUE_ERROR: Bot #2 → '💯' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:06] QUEUE_ERROR: Bot #2 → '💯' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:06] QUEUE_ERROR: Bot #2 → '💯' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:06] QUEUE_ERROR: Bot #2 → '💯' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:06] QUEUE_ERROR: Bot #2 → '💯' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:06] QUEUE_ERROR: Bot #2 → '💯' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:06] QUEUE_ERROR: Bot #2 → '💯' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:06] QUEUE_ERROR: Bot #2 → '💯' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:06] QUEUE_ERROR: Bot #2 → '💯' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:06] QUEUE_ERROR: Bot #2 → '💯' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:06] QUEUE_ERROR: Bot #2 → '💯' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:06] QUEUE_ERROR: Bot #2 → '💯' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:07] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 264, Delay: 44s)
[2025-08-07 06:17:07] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 41 remaining
[2025-08-07 06:17:07] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 264, Delay: 44s)
[2025-08-07 06:17:07] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 41 remaining
[2025-08-07 06:17:07] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 264, Delay: 44s)
[2025-08-07 06:17:07] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 41 remaining
[2025-08-07 06:17:07] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 264, Delay: 44s)
[2025-08-07 06:17:07] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 41 remaining
[2025-08-07 06:17:07] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 264, Delay: 44s)
[2025-08-07 06:17:07] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 41 remaining
[2025-08-07 06:17:14] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 264, Delay: 44s)
[2025-08-07 06:17:14] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 41 remaining
[2025-08-07 06:17:14] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 264, Delay: 44s)
[2025-08-07 06:17:14] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 41 remaining
[2025-08-07 06:17:14] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 264, Delay: 44s)
[2025-08-07 06:17:14] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 41 remaining
[2025-08-07 06:17:14] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 264, Delay: 44s)
[2025-08-07 06:17:14] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 41 remaining
[2025-08-07 06:17:14] QUEUE_ERROR: Bot #3 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:14] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 41 remaining
[2025-08-07 06:17:14] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 264, Delay: 44s)
[2025-08-07 06:17:14] QUEUE_ERROR: Bot #3 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:14] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 41 remaining
[2025-08-07 06:17:14] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 41 remaining
[2025-08-07 06:17:14] QUEUE_ERROR: Bot #3 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:14] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 41 remaining
[2025-08-07 06:17:14] QUEUE_ERROR: Bot #3 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:14] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 41 remaining
[2025-08-07 06:17:14] QUEUE_ERROR: Bot #3 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:14] QUEUE_ERROR: Bot #3 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:14] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 41 remaining
[2025-08-07 06:17:14] QUEUE_ERROR: Bot #3 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:14] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 41 remaining
[2025-08-07 06:17:14] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 41 remaining
[2025-08-07 06:17:14] QUEUE_ERROR: Bot #3 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:14] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 41 remaining
[2025-08-07 06:17:14] QUEUE_ERROR: Bot #3 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:14] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 41 remaining
[2025-08-07 06:17:14] QUEUE_ERROR: Bot #3 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:14] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 41 remaining
[2025-08-07 06:17:15] QUEUE_ERROR: Bot #3 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:15] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 41 remaining
[2025-08-07 06:17:15] QUEUE_ERROR: Bot #3 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:15] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 41 remaining
[2025-08-07 06:17:15] QUEUE_ERROR: Bot #3 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:15] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 41 remaining
[2025-08-07 06:17:15] QUEUE_ERROR: Bot #3 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:17:15] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 41 remaining
[2025-08-07 06:17:17] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 06:17:17] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:17] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 06:17:17] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:17] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 06:17:17] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:17] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 06:17:17] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:17] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 06:17:17] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:22] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 06:17:22] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:22] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 06:17:22] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:22] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 06:17:22] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:22] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 06:17:22] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:23] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 06:17:23] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:26] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:26] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:26] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:26] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:26] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:26] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:26] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:26] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:26] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:26] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:26] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:26] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:26] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:26] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:26] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:26] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:26] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:26] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:26] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:26] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:26] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:26] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:27] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:27] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:27] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:27] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:27] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:27] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:27] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:27] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:27] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:27] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:17:27] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP Unknown: No response
[2025-08-07 06:17:27] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 40 remaining
[2025-08-07 06:18:05] QUEUE_SUCCESS: Bot #5 → '❤️' (Channel: -1002888471861, Message: 264, Delay: 108s)
[2025-08-07 06:18:06] QUEUE_SUCCESS: Bot #6 → '❤️' (Channel: -1002888471861, Message: 264, Delay: 108s)
[2025-08-07 06:18:06] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 38 remaining
[2025-08-07 06:18:06] POST_STATS: -1002888471861_264 - 2 successful, 0 errors
[2025-08-07 06:18:29] QUEUE_SUCCESS: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 132s)
[2025-08-07 06:18:29] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 37 remaining
[2025-08-07 06:18:29] WEBHOOK_RECEIVED: Bot #20 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:29
[2025-08-07 06:18:29] SEQUENTIAL_SKIP: Bot #20 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:29] QUEUE_SUCCESS: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 132s)
[2025-08-07 06:18:29] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 37 remaining
[2025-08-07 06:18:29] WEBHOOK_RECEIVED: Bot #30 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:29
[2025-08-07 06:18:29] SEQUENTIAL_SKIP: Bot #30 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:29] QUEUE_SUCCESS: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 132s)
[2025-08-07 06:18:29] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 37 remaining
[2025-08-07 06:18:29] WEBHOOK_RECEIVED: Bot #25 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:29
[2025-08-07 06:18:29] SEQUENTIAL_SKIP: Bot #25 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:29] QUEUE_SUCCESS: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 132s)
[2025-08-07 06:18:29] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 37 remaining
[2025-08-07 06:18:29] WEBHOOK_RECEIVED: Bot #5 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:29
[2025-08-07 06:18:29] SEQUENTIAL_SKIP: Bot #5 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:29] QUEUE_SUCCESS: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 132s)
[2025-08-07 06:18:29] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 37 remaining
[2025-08-07 06:18:29] WEBHOOK_RECEIVED: Bot #4 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:29
[2025-08-07 06:18:29] SEQUENTIAL_SKIP: Bot #4 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:34] QUEUE_SUCCESS: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 132s)
[2025-08-07 06:18:34] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 37 remaining
[2025-08-07 06:18:34] WEBHOOK_RECEIVED: Bot #41 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:34
[2025-08-07 06:18:34] SEQUENTIAL_SKIP: Bot #41 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:34] QUEUE_SUCCESS: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 132s)
[2025-08-07 06:18:34] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 37 remaining
[2025-08-07 06:18:34] WEBHOOK_RECEIVED: Bot #23 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:34
[2025-08-07 06:18:34] SEQUENTIAL_SKIP: Bot #23 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:34] QUEUE_SUCCESS: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 132s)
[2025-08-07 06:18:34] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 37 remaining
[2025-08-07 06:18:34] WEBHOOK_RECEIVED: Bot #2 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:34
[2025-08-07 06:18:34] SEQUENTIAL_SKIP: Bot #2 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:34] QUEUE_SUCCESS: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 132s)
[2025-08-07 06:18:34] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 37 remaining
[2025-08-07 06:18:34] WEBHOOK_RECEIVED: Bot #19 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:34
[2025-08-07 06:18:34] SEQUENTIAL_SKIP: Bot #19 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:34] QUEUE_ERROR: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:18:34] QUEUE_ERROR: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:18:34] QUEUE_ERROR: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:18:34] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 37 remaining
[2025-08-07 06:18:34] WEBHOOK_RECEIVED: Bot #1 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:34
[2025-08-07 06:18:34] SEQUENTIAL_START: Bot #1 starting sequential processing for Channel: -1002888471861, Message: 265
[2025-08-07 06:18:34] QUEUE_CREATE_START: Bot #1 creating queue for Channel: -1002888471861, Message: 265
[2025-08-07 06:18:34] QUEUE_ERROR: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:18:34] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 37 remaining
[2025-08-07 06:18:34] WEBHOOK_RECEIVED: Bot #14 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:34
[2025-08-07 06:18:34] SEQUENTIAL_SKIP: Bot #14 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:34] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 37 remaining
[2025-08-07 06:18:34] WEBHOOK_RECEIVED: Bot #9 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:34
[2025-08-07 06:18:34] SEQUENTIAL_SKIP: Bot #9 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:34] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 37 remaining
[2025-08-07 06:18:34] WEBHOOK_RECEIVED: Bot #12 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:34
[2025-08-07 06:18:34] QUEUE_ERROR: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:18:34] SEQUENTIAL_SKIP: Bot #12 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:34] QUEUE_SUCCESS: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 132s)
[2025-08-07 06:18:34] QUEUE_ERROR: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:18:34] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 37 remaining
[2025-08-07 06:18:34] WEBHOOK_RECEIVED: Bot #6 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:34
[2025-08-07 06:18:34] SEQUENTIAL_SKIP: Bot #6 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:34] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 37 remaining
[2025-08-07 06:18:34] QUEUE_ERROR: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:18:34] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 37 remaining
[2025-08-07 06:18:34] WEBHOOK_RECEIVED: Bot #37 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:34
[2025-08-07 06:18:34] WEBHOOK_RECEIVED: Bot #33 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:34
[2025-08-07 06:18:34] SEQUENTIAL_SKIP: Bot #37 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:34] SEQUENTIAL_SKIP: Bot #33 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:34] QUEUE_ERROR: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:18:34] QUEUE_CREATE_SUCCESS: Added 43 reactions for Channel: -1002888471861, Message: 265, Duration: 860s
[2025-08-07 06:18:34] QUEUE_ERROR: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:18:34] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 37 remaining
[2025-08-07 06:18:34] WEBHOOK_RECEIVED: Bot #44 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:34
[2025-08-07 06:18:34] SEQUENTIAL_SKIP: Bot #44 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:34] QUEUE_ERROR: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:18:34] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 37 remaining
[2025-08-07 06:18:34] WEBHOOK_RECEIVED: Bot #7 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:34
[2025-08-07 06:18:34] SEQUENTIAL_SKIP: Bot #7 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:34] QUEUE_ERROR: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:18:34] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 37 remaining
[2025-08-07 06:18:34] WEBHOOK_RECEIVED: Bot #10 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:34
[2025-08-07 06:18:34] SEQUENTIAL_SKIP: Bot #10 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:34] QUEUE_ERROR: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:18:34] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 37 remaining
[2025-08-07 06:18:34] WEBHOOK_RECEIVED: Bot #17 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:34
[2025-08-07 06:18:34] SEQUENTIAL_SKIP: Bot #17 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:34] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 37 remaining
[2025-08-07 06:18:34] WEBHOOK_RECEIVED: Bot #35 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:34
[2025-08-07 06:18:34] SEQUENTIAL_SKIP: Bot #35 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:34] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 37 remaining
[2025-08-07 06:18:34] WEBHOOK_RECEIVED: Bot #42 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:34
[2025-08-07 06:18:34] SEQUENTIAL_SKIP: Bot #42 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:34] QUEUE_ERROR: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:18:34] QUEUE_ERROR: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:18:34] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 37 remaining
[2025-08-07 06:18:34] QUEUE_ERROR: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:18:34] WEBHOOK_RECEIVED: Bot #3 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:34
[2025-08-07 06:18:34] SEQUENTIAL_SKIP: Bot #3 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:34] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 37 remaining
[2025-08-07 06:18:34] WEBHOOK_RECEIVED: Bot #11 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:34
[2025-08-07 06:18:34] SEQUENTIAL_SKIP: Bot #11 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:34] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 37 remaining
[2025-08-07 06:18:34] WEBHOOK_RECEIVED: Bot #39 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:34
[2025-08-07 06:18:34] SEQUENTIAL_SKIP: Bot #39 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:34] QUEUE_ERROR: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:18:34] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 37 remaining
[2025-08-07 06:18:34] WEBHOOK_RECEIVED: Bot #36 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:34
[2025-08-07 06:18:34] SEQUENTIAL_SKIP: Bot #36 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:34] QUEUE_ERROR: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:18:34] QUEUE_ERROR: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:18:34] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 37 remaining
[2025-08-07 06:18:34] WEBHOOK_RECEIVED: Bot #13 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:34
[2025-08-07 06:18:34] SEQUENTIAL_SKIP: Bot #13 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:34] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 37 remaining
[2025-08-07 06:18:34] WEBHOOK_RECEIVED: Bot #24 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:34
[2025-08-07 06:18:34] SEQUENTIAL_SKIP: Bot #24 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:34] QUEUE_ERROR: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:18:34] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 37 remaining
[2025-08-07 06:18:34] WEBHOOK_RECEIVED: Bot #21 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:34
[2025-08-07 06:18:34] SEQUENTIAL_SKIP: Bot #21 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:34] QUEUE_ERROR: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:18:34] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 37 remaining
[2025-08-07 06:18:34] WEBHOOK_RECEIVED: Bot #34 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:34
[2025-08-07 06:18:34] SEQUENTIAL_SKIP: Bot #34 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:34] QUEUE_ERROR: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:18:34] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 37 remaining
[2025-08-07 06:18:34] WEBHOOK_RECEIVED: Bot #15 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:34
[2025-08-07 06:18:34] SEQUENTIAL_SKIP: Bot #15 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:35] QUEUE_ERROR: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:18:35] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 37 remaining
[2025-08-07 06:18:35] WEBHOOK_RECEIVED: Bot #16 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:35
[2025-08-07 06:18:35] SEQUENTIAL_SKIP: Bot #16 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:35] QUEUE_ERROR: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:18:35] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 37 remaining
[2025-08-07 06:18:35] WEBHOOK_RECEIVED: Bot #8 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:35
[2025-08-07 06:18:35] SEQUENTIAL_SKIP: Bot #8 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:35] QUEUE_ERROR: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:18:35] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 37 remaining
[2025-08-07 06:18:35] WEBHOOK_RECEIVED: Bot #40 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:35
[2025-08-07 06:18:35] SEQUENTIAL_SKIP: Bot #40 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:35] QUEUE_ERROR: Bot #7 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:18:35] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 37 remaining
[2025-08-07 06:18:35] WEBHOOK_RECEIVED: Bot #38 received message from Channel: -1002888471861, Message: 265, Timestamp: 2025-08-07 06:18:35
[2025-08-07 06:18:35] SEQUENTIAL_SKIP: Bot #38 skipping immediate reaction for Channel: -1002888471861, Message: 265 (will process via queue)
[2025-08-07 06:18:35] SEQUENTIAL_BOT1_SUCCESS: Bot #1 → '❤️' (Channel: -1002888471861, Message: 265) - IMMEDIATE
[2025-08-07 06:19:04] QUEUE_SUCCESS: Bot #8 → '💯' (Channel: -1002888471861, Message: 264, Delay: 168s)
[2025-08-07 06:19:05] QUEUE_SUCCESS: Bot #9 → '🤩' (Channel: -1002888471861, Message: 264, Delay: 168s)
[2025-08-07 06:19:05] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 35 remaining
[2025-08-07 06:19:05] POST_STATS: -1002888471861_264 - 2 successful, 0 errors
[2025-08-07 06:19:42] QUEUE_SUCCESS: Bot #10 → '😘' (Channel: -1002888471861, Message: 264, Delay: 205s)
[2025-08-07 06:19:42] QUEUE_SUCCESS: Bot #10 → '😘' (Channel: -1002888471861, Message: 264, Delay: 205s)
[2025-08-07 06:19:42] QUEUE_SUCCESS: Bot #10 → '😘' (Channel: -1002888471861, Message: 264, Delay: 205s)
[2025-08-07 06:19:42] QUEUE_SUCCESS: Bot #10 → '😘' (Channel: -1002888471861, Message: 264, Delay: 205s)
[2025-08-07 06:19:42] QUEUE_SUCCESS: Bot #10 → '😘' (Channel: -1002888471861, Message: 264, Delay: 205s)
[2025-08-07 06:19:43] QUEUE_SUCCESS: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 205s)
[2025-08-07 06:19:43] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 33 remaining
[2025-08-07 06:19:43] QUEUE_SUCCESS: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 205s)
[2025-08-07 06:19:43] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 33 remaining
[2025-08-07 06:19:43] QUEUE_SUCCESS: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 205s)
[2025-08-07 06:19:43] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 33 remaining
[2025-08-07 06:19:43] QUEUE_SUCCESS: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 205s)
[2025-08-07 06:19:43] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 33 remaining
[2025-08-07 06:19:43] QUEUE_SUCCESS: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 205s)
[2025-08-07 06:19:43] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 33 remaining
[2025-08-07 06:19:47] QUEUE_SUCCESS: Bot #10 → '😘' (Channel: -1002888471861, Message: 264, Delay: 205s)
[2025-08-07 06:19:47] QUEUE_SUCCESS: Bot #10 → '😘' (Channel: -1002888471861, Message: 264, Delay: 205s)
[2025-08-07 06:19:47] QUEUE_SUCCESS: Bot #10 → '😘' (Channel: -1002888471861, Message: 264, Delay: 205s)
[2025-08-07 06:19:47] QUEUE_ERROR: Bot #10 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:47] QUEUE_SUCCESS: Bot #10 → '😘' (Channel: -1002888471861, Message: 264, Delay: 205s)
[2025-08-07 06:19:47] QUEUE_ERROR: Bot #10 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:47] QUEUE_ERROR: Bot #10 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:47] QUEUE_ERROR: Bot #10 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:47] QUEUE_ERROR: Bot #10 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:47] QUEUE_SUCCESS: Bot #10 → '😘' (Channel: -1002888471861, Message: 264, Delay: 205s)
[2025-08-07 06:19:47] QUEUE_ERROR: Bot #10 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:47] QUEUE_ERROR: Bot #10 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:47] QUEUE_ERROR: Bot #10 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:47] QUEUE_ERROR: Bot #10 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:47] QUEUE_ERROR: Bot #10 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:47] QUEUE_ERROR: Bot #10 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:47] QUEUE_ERROR: Bot #10 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:47] QUEUE_ERROR: Bot #10 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:47] QUEUE_ERROR: Bot #10 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:47] QUEUE_ERROR: Bot #10 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:47] QUEUE_ERROR: Bot #10 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:47] QUEUE_ERROR: Bot #10 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:47] QUEUE_ERROR: Bot #10 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:47] QUEUE_ERROR: Bot #10 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:48] QUEUE_SUCCESS: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 205s)
[2025-08-07 06:19:48] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 33 remaining
[2025-08-07 06:19:48] QUEUE_SUCCESS: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 205s)
[2025-08-07 06:19:48] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 33 remaining
[2025-08-07 06:19:48] QUEUE_SUCCESS: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 205s)
[2025-08-07 06:19:48] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 33 remaining
[2025-08-07 06:19:48] QUEUE_SUCCESS: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 205s)
[2025-08-07 06:19:48] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 33 remaining
[2025-08-07 06:19:48] QUEUE_SUCCESS: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 205s)
[2025-08-07 06:19:48] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 33 remaining
[2025-08-07 06:19:53] QUEUE_SUCCESS: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 205s)
[2025-08-07 06:19:53] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 33 remaining
[2025-08-07 06:19:53] QUEUE_SUCCESS: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 206s)
[2025-08-07 06:19:53] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 33 remaining
[2025-08-07 06:19:53] QUEUE_SUCCESS: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 205s)
[2025-08-07 06:19:53] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 33 remaining
[2025-08-07 06:19:53] QUEUE_SUCCESS: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 205s)
[2025-08-07 06:19:53] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 33 remaining
[2025-08-07 06:19:53] QUEUE_ERROR: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:53] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 33 remaining
[2025-08-07 06:19:53] QUEUE_ERROR: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:53] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 33 remaining
[2025-08-07 06:19:53] QUEUE_ERROR: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:53] QUEUE_ERROR: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:53] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 33 remaining
[2025-08-07 06:19:53] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 33 remaining
[2025-08-07 06:19:53] QUEUE_SUCCESS: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 205s)
[2025-08-07 06:19:53] QUEUE_ERROR: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:53] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 33 remaining
[2025-08-07 06:19:53] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 33 remaining
[2025-08-07 06:19:53] QUEUE_ERROR: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:53] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 33 remaining
[2025-08-07 06:19:53] QUEUE_ERROR: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:53] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 33 remaining
[2025-08-07 06:19:53] QUEUE_ERROR: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:53] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 33 remaining
[2025-08-07 06:19:53] QUEUE_ERROR: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:53] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 33 remaining
[2025-08-07 06:19:53] QUEUE_ERROR: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:53] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 33 remaining
[2025-08-07 06:19:53] QUEUE_ERROR: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:53] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 33 remaining
[2025-08-07 06:19:53] QUEUE_ERROR: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:53] QUEUE_ERROR: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:53] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 33 remaining
[2025-08-07 06:19:53] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 33 remaining
[2025-08-07 06:19:53] QUEUE_ERROR: Bot #11 → '🥰' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:19:53] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 33 remaining
[2025-08-07 06:20:03] QUEUE_SUCCESS: Bot #12 → '🤩' (Channel: -1002888471861, Message: 264, Delay: 226s)
[2025-08-07 06:20:03] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 32 remaining
[2025-08-07 06:20:03] POST_STATS: -1002888471861_264 - 1 successful, 0 errors
[2025-08-07 06:21:04] QUEUE_SUCCESS: Bot #13 → '🤩' (Channel: -1002888471861, Message: 264, Delay: 287s)
[2025-08-07 06:21:05] QUEUE_SUCCESS: Bot #14 → '🤩' (Channel: -1002888471861, Message: 264, Delay: 287s)
[2025-08-07 06:21:06] QUEUE_SUCCESS: Bot #15 → '🏆' (Channel: -1002888471861, Message: 264, Delay: 287s)
[2025-08-07 06:21:06] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 29 remaining
[2025-08-07 06:21:06] POST_STATS: -1002888471861_264 - 3 successful, 0 errors
[2025-08-07 06:22:05] QUEUE_SUCCESS: Bot #16 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 348s)
[2025-08-07 06:22:06] QUEUE_SUCCESS: Bot #17 → '😘' (Channel: -1002888471861, Message: 264, Delay: 348s)
[2025-08-07 06:22:07] QUEUE_ERROR: Bot #18 → '🤩' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:22:07] QUEUE_SUMMARY: Processed 2 reactions, 1 errors, 26 remaining
[2025-08-07 06:22:07] POST_STATS: -1002888471861_264 - 2 successful, 1 errors
[2025-08-07 06:23:05] QUEUE_SUCCESS: Bot #19 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 408s)
[2025-08-07 06:23:06] QUEUE_SUCCESS: Bot #20 → '🏆' (Channel: -1002888471861, Message: 264, Delay: 408s)
[2025-08-07 06:23:07] QUEUE_SUCCESS: Bot #21 → '❤️' (Channel: -1002888471861, Message: 264, Delay: 408s)
[2025-08-07 06:23:07] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 23 remaining
[2025-08-07 06:23:07] POST_STATS: -1002888471861_264 - 3 successful, 0 errors
[2025-08-07 06:24:04] QUEUE_ERROR: Bot #22 → '😘' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:05] QUEUE_SUCCESS: Bot #23 → '🏆' (Channel: -1002888471861, Message: 264, Delay: 468s)
[2025-08-07 06:24:06] QUEUE_SUCCESS: Bot #24 → '🏆' (Channel: -1002888471861, Message: 264, Delay: 468s)
[2025-08-07 06:24:06] QUEUE_SUMMARY: Processed 2 reactions, 1 errors, 20 remaining
[2025-08-07 06:24:06] POST_STATS: -1002888471861_264 - 2 successful, 1 errors
[2025-08-07 06:24:20] QUEUE_SUCCESS: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264, Delay: 483s)
[2025-08-07 06:24:20] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 19 remaining
[2025-08-07 06:24:20] QUEUE_SUCCESS: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264, Delay: 483s)
[2025-08-07 06:24:20] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 19 remaining
[2025-08-07 06:24:20] QUEUE_SUCCESS: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264, Delay: 483s)
[2025-08-07 06:24:20] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 19 remaining
[2025-08-07 06:24:20] QUEUE_SUCCESS: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264, Delay: 483s)
[2025-08-07 06:24:20] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 19 remaining
[2025-08-07 06:24:20] QUEUE_SUCCESS: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264, Delay: 483s)
[2025-08-07 06:24:20] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 19 remaining
[2025-08-07 06:24:25] QUEUE_SUCCESS: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264, Delay: 483s)
[2025-08-07 06:24:25] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 19 remaining
[2025-08-07 06:24:25] QUEUE_SUCCESS: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264, Delay: 483s)
[2025-08-07 06:24:25] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 19 remaining
[2025-08-07 06:24:25] QUEUE_SUCCESS: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264, Delay: 483s)
[2025-08-07 06:24:25] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 19 remaining
[2025-08-07 06:24:25] QUEUE_SUCCESS: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264, Delay: 483s)
[2025-08-07 06:24:25] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 19 remaining
[2025-08-07 06:24:25] QUEUE_ERROR: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:24:25] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 19 remaining
[2025-08-07 06:24:25] QUEUE_ERROR: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:24:25] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 19 remaining
[2025-08-07 06:24:25] QUEUE_ERROR: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:24:25] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 19 remaining
[2025-08-07 06:24:25] QUEUE_ERROR: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:24:25] QUEUE_ERROR: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:24:25] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 19 remaining
[2025-08-07 06:24:25] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 19 remaining
[2025-08-07 06:24:25] QUEUE_ERROR: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:24:25] QUEUE_ERROR: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:24:25] QUEUE_ERROR: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:24:25] QUEUE_SUCCESS: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264, Delay: 483s)
[2025-08-07 06:24:25] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 19 remaining
[2025-08-07 06:24:25] QUEUE_ERROR: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:24:25] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 19 remaining
[2025-08-07 06:24:25] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 19 remaining
[2025-08-07 06:24:25] QUEUE_ERROR: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:24:25] QUEUE_ERROR: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:24:25] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 19 remaining
[2025-08-07 06:24:25] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 19 remaining
[2025-08-07 06:24:25] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 19 remaining
[2025-08-07 06:24:25] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 19 remaining
[2025-08-07 06:24:26] QUEUE_ERROR: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:24:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 19 remaining
[2025-08-07 06:24:26] QUEUE_ERROR: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:24:26] QUEUE_ERROR: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:24:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 19 remaining
[2025-08-07 06:24:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 19 remaining
[2025-08-07 06:24:26] QUEUE_ERROR: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:24:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 19 remaining
[2025-08-07 06:24:26] QUEUE_ERROR: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:24:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 19 remaining
[2025-08-07 06:24:26] QUEUE_ERROR: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:24:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 19 remaining
[2025-08-07 06:24:26] QUEUE_ERROR: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:24:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 19 remaining
[2025-08-07 06:24:26] QUEUE_ERROR: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:24:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 19 remaining
[2025-08-07 06:24:26] QUEUE_ERROR: Bot #25 → '🤩' (Channel: -1002888471861, Message: 264) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:24:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 19 remaining
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_ERROR: Bot #26 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:24:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:25:06] QUEUE_ERROR: Bot #27 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:25:06] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 17 remaining
[2025-08-07 06:25:06] POST_STATS: -1002888471861_264 - 0 successful, 1 errors
[2025-08-07 06:26:05] QUEUE_ERROR: Bot #28 → '🤩' (Channel: -1002888471861, Message: 264) - HTTP 401: Unauthorized
[2025-08-07 06:26:06] QUEUE_ERROR: Bot #29 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:26:07] QUEUE_SUCCESS: Bot #30 → '💯' (Channel: -1002888471861, Message: 264, Delay: 587s)
[2025-08-07 06:26:07] QUEUE_SUMMARY: Processed 1 reactions, 2 errors, 14 remaining
[2025-08-07 06:26:07] POST_STATS: -1002888471861_264 - 1 successful, 2 errors
[2025-08-07 06:27:05] QUEUE_ERROR: Bot #31 → '🤩' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:27:06] QUEUE_ERROR: Bot #32 → '❤️' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:27:07] QUEUE_SUCCESS: Bot #33 → '🏆' (Channel: -1002888471861, Message: 264, Delay: 648s)
[2025-08-07 06:27:07] QUEUE_SUMMARY: Processed 1 reactions, 2 errors, 11 remaining
[2025-08-07 06:27:07] POST_STATS: -1002888471861_264 - 1 successful, 2 errors
[2025-08-07 06:28:07] QUEUE_ERROR: Bot #34 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 06:28:08] QUEUE_SUCCESS: Bot #35 → '🤩' (Channel: -1002888471861, Message: 264, Delay: 710s)
[2025-08-07 06:28:09] QUEUE_SUCCESS: Bot #36 → '🏆' (Channel: -1002888471861, Message: 264, Delay: 710s)
[2025-08-07 06:28:09] QUEUE_SUMMARY: Processed 2 reactions, 1 errors, 8 remaining
[2025-08-07 06:28:09] POST_STATS: -1002888471861_264 - 2 successful, 1 errors
[2025-08-07 06:29:04] QUEUE_ERROR: Bot #37 → '🎉' (Channel: -1002888471861, Message: 264) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 06:29:05] QUEUE_SUCCESS: Bot #38 → '🥰' (Channel: -1002888471861, Message: 264, Delay: 767s)
[2025-08-07 06:29:05] QUEUE_SUCCESS: Bot #39 → '😘' (Channel: -1002888471861, Message: 264, Delay: 767s)
[2025-08-07 06:29:05] QUEUE_SUMMARY: Processed 2 reactions, 1 errors, 5 remaining
[2025-08-07 06:29:05] POST_STATS: -1002888471861_264 - 2 successful, 1 errors
[2025-08-07 06:30:06] QUEUE_SUCCESS: Bot #40 → '😘' (Channel: -1002888471861, Message: 264, Delay: 829s)
[2025-08-07 06:30:07] QUEUE_SUCCESS: Bot #41 → '😘' (Channel: -1002888471861, Message: 264, Delay: 829s)
[2025-08-07 06:30:08] QUEUE_SUCCESS: Bot #42 → '💯' (Channel: -1002888471861, Message: 264, Delay: 829s)
[2025-08-07 06:30:08] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 2 remaining
[2025-08-07 06:30:08] POST_STATS: -1002888471861_264 - 3 successful, 0 errors
[2025-08-07 06:31:07] QUEUE_ERROR: Bot #43 → '🤩' (Channel: -1002888471861, Message: 264) - HTTP 401: Unauthorized
[2025-08-07 06:31:08] QUEUE_SUCCESS: Bot #44 → '😘' (Channel: -1002888471861, Message: 264, Delay: 888s)
[2025-08-07 06:31:08] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 0 remaining
[2025-08-07 06:31:08] POST_STATS: -1002888471861_264 - 1 successful, 1 errors
[2025-08-07 06:44:32] WEBHOOK_RECEIVED: Bot #24 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:32
[2025-08-07 06:44:32] SEQUENTIAL_SKIP: Bot #24 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:32] WEBHOOK_RECEIVED: Bot #23 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:32
[2025-08-07 06:44:32] SEQUENTIAL_SKIP: Bot #23 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:32] WEBHOOK_RECEIVED: Bot #25 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:32
[2025-08-07 06:44:32] SEQUENTIAL_SKIP: Bot #25 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:32] WEBHOOK_RECEIVED: Bot #19 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:32
[2025-08-07 06:44:32] SEQUENTIAL_SKIP: Bot #19 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #9 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #9 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #37 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #37 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #16 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #16 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #36 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #36 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #14 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #14 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #17 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #17 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #10 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #10 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #39 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #39 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #13 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #13 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #11 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #11 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #40 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #40 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #12 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #12 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #6 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #6 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #41 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #41 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #33 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #33 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #34 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #34 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #5 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #5 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #3 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #20 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #20 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #3 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #1 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_START: Bot #1 starting sequential processing for Channel: -1002888471861, Message: 266
[2025-08-07 06:44:33] QUEUE_CREATE_START: Bot #1 creating queue for Channel: -1002888471861, Message: 266
[2025-08-07 06:44:33] QUEUE_CREATE_SUCCESS: Added 43 reactions for Channel: -1002888471861, Message: 266, Duration: 860s
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #2 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #2 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #30 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #30 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #4 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #4 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #21 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #21 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #15 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #15 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #44 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #44 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #42 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #42 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #8 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #8 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #7 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #7 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #35 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #35 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:33] WEBHOOK_RECEIVED: Bot #38 received message from Channel: -1002888471861, Message: 266, Timestamp: 2025-08-07 06:44:33
[2025-08-07 06:44:33] SEQUENTIAL_SKIP: Bot #38 skipping immediate reaction for Channel: -1002888471861, Message: 266 (will process via queue)
[2025-08-07 06:44:34] SEQUENTIAL_BOT1_SUCCESS: Bot #1 → '🏆' (Channel: -1002888471861, Message: 266) - IMMEDIATE
[2025-08-07 06:44:38] WEBHOOK_RECEIVED: Bot #24 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:38
[2025-08-07 06:44:38] SEQUENTIAL_SKIP: Bot #24 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:38] WEBHOOK_RECEIVED: Bot #25 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:38
[2025-08-07 06:44:38] SEQUENTIAL_SKIP: Bot #25 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:38] WEBHOOK_RECEIVED: Bot #3 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:38
[2025-08-07 06:44:38] SEQUENTIAL_SKIP: Bot #3 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:38] WEBHOOK_RECEIVED: Bot #1 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:38
[2025-08-07 06:44:38] SEQUENTIAL_START: Bot #1 starting sequential processing for Channel: -1002888471861, Message: 267
[2025-08-07 06:44:38] QUEUE_CREATE_START: Bot #1 creating queue for Channel: -1002888471861, Message: 267
[2025-08-07 06:44:38] QUEUE_CREATE_SUCCESS: Added 43 reactions for Channel: -1002888471861, Message: 267, Duration: 860s
[2025-08-07 06:44:38] WEBHOOK_RECEIVED: Bot #2 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:38
[2025-08-07 06:44:38] SEQUENTIAL_SKIP: Bot #2 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:38] WEBHOOK_RECEIVED: Bot #30 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:38
[2025-08-07 06:44:38] SEQUENTIAL_SKIP: Bot #30 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:38] WEBHOOK_RECEIVED: Bot #5 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:38
[2025-08-07 06:44:38] SEQUENTIAL_SKIP: Bot #5 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:38] WEBHOOK_RECEIVED: Bot #23 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:38
[2025-08-07 06:44:38] SEQUENTIAL_SKIP: Bot #23 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:38] WEBHOOK_RECEIVED: Bot #19 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:38
[2025-08-07 06:44:38] SEQUENTIAL_SKIP: Bot #19 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:38] WEBHOOK_RECEIVED: Bot #33 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:38
[2025-08-07 06:44:38] SEQUENTIAL_SKIP: Bot #33 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:38] WEBHOOK_RECEIVED: Bot #20 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:38
[2025-08-07 06:44:38] SEQUENTIAL_SKIP: Bot #20 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:38] WEBHOOK_RECEIVED: Bot #21 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:38
[2025-08-07 06:44:38] SEQUENTIAL_SKIP: Bot #21 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:38] WEBHOOK_RECEIVED: Bot #4 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:38
[2025-08-07 06:44:38] SEQUENTIAL_SKIP: Bot #4 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:38] WEBHOOK_RECEIVED: Bot #34 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:38
[2025-08-07 06:44:38] SEQUENTIAL_SKIP: Bot #34 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:38] WEBHOOK_RECEIVED: Bot #41 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:38
[2025-08-07 06:44:38] SEQUENTIAL_SKIP: Bot #41 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:39] WEBHOOK_RECEIVED: Bot #42 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:39
[2025-08-07 06:44:39] SEQUENTIAL_SKIP: Bot #42 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:39] WEBHOOK_RECEIVED: Bot #14 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:39
[2025-08-07 06:44:39] SEQUENTIAL_SKIP: Bot #14 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:39] WEBHOOK_RECEIVED: Bot #39 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:39
[2025-08-07 06:44:39] SEQUENTIAL_SKIP: Bot #39 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:39] WEBHOOK_RECEIVED: Bot #44 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:39
[2025-08-07 06:44:39] SEQUENTIAL_SKIP: Bot #44 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:39] WEBHOOK_RECEIVED: Bot #15 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:39
[2025-08-07 06:44:39] SEQUENTIAL_SKIP: Bot #15 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:39] WEBHOOK_RECEIVED: Bot #11 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:39
[2025-08-07 06:44:39] SEQUENTIAL_SKIP: Bot #11 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:39] WEBHOOK_RECEIVED: Bot #17 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:39
[2025-08-07 06:44:39] SEQUENTIAL_SKIP: Bot #17 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:39] WEBHOOK_RECEIVED: Bot #10 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:39
[2025-08-07 06:44:39] SEQUENTIAL_SKIP: Bot #10 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:39] WEBHOOK_RECEIVED: Bot #36 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:39
[2025-08-07 06:44:39] SEQUENTIAL_SKIP: Bot #36 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:39] WEBHOOK_RECEIVED: Bot #37 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:39
[2025-08-07 06:44:39] SEQUENTIAL_SKIP: Bot #37 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:39] WEBHOOK_RECEIVED: Bot #7 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:39
[2025-08-07 06:44:39] SEQUENTIAL_SKIP: Bot #7 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:39] WEBHOOK_RECEIVED: Bot #35 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:39
[2025-08-07 06:44:39] SEQUENTIAL_SKIP: Bot #35 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:39] WEBHOOK_RECEIVED: Bot #12 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:39
[2025-08-07 06:44:39] SEQUENTIAL_SKIP: Bot #12 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:39] WEBHOOK_RECEIVED: Bot #13 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:39
[2025-08-07 06:44:39] SEQUENTIAL_SKIP: Bot #13 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:39] WEBHOOK_RECEIVED: Bot #38 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:39
[2025-08-07 06:44:39] SEQUENTIAL_SKIP: Bot #38 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:39] WEBHOOK_RECEIVED: Bot #40 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:39
[2025-08-07 06:44:39] SEQUENTIAL_SKIP: Bot #40 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:39] WEBHOOK_RECEIVED: Bot #8 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:39
[2025-08-07 06:44:39] SEQUENTIAL_SKIP: Bot #8 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:39] WEBHOOK_RECEIVED: Bot #9 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:39
[2025-08-07 06:44:39] SEQUENTIAL_SKIP: Bot #9 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:39] WEBHOOK_RECEIVED: Bot #16 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:39
[2025-08-07 06:44:39] SEQUENTIAL_SKIP: Bot #16 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:39] WEBHOOK_RECEIVED: Bot #6 received message from Channel: -1002888471861, Message: 267, Timestamp: 2025-08-07 06:44:39
[2025-08-07 06:44:39] SEQUENTIAL_SKIP: Bot #6 skipping immediate reaction for Channel: -1002888471861, Message: 267 (will process via queue)
[2025-08-07 06:44:39] SEQUENTIAL_BOT1_ERROR: Bot #1 → '🎉' (Channel: -1002888471861, Message: 267) - Bad Request: REACTION_INVALID
[2025-08-07 06:44:43] WEBHOOK_RECEIVED: Bot #24 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:43
[2025-08-07 06:44:43] SEQUENTIAL_SKIP: Bot #24 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:43] WEBHOOK_RECEIVED: Bot #20 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:43
[2025-08-07 06:44:43] SEQUENTIAL_SKIP: Bot #20 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:43] WEBHOOK_RECEIVED: Bot #34 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:43
[2025-08-07 06:44:43] SEQUENTIAL_SKIP: Bot #34 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:43] WEBHOOK_RECEIVED: Bot #25 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:43
[2025-08-07 06:44:43] SEQUENTIAL_SKIP: Bot #25 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:43] WEBHOOK_RECEIVED: Bot #30 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:43
[2025-08-07 06:44:43] SEQUENTIAL_SKIP: Bot #30 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:43] WEBHOOK_RECEIVED: Bot #41 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:43
[2025-08-07 06:44:43] SEQUENTIAL_SKIP: Bot #41 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:43] WEBHOOK_RECEIVED: Bot #5 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:43
[2025-08-07 06:44:43] SEQUENTIAL_SKIP: Bot #5 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:43] WEBHOOK_RECEIVED: Bot #21 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:43
[2025-08-07 06:44:43] SEQUENTIAL_SKIP: Bot #21 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:43] WEBHOOK_RECEIVED: Bot #19 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:43
[2025-08-07 06:44:43] SEQUENTIAL_SKIP: Bot #19 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:43] WEBHOOK_RECEIVED: Bot #23 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:43
[2025-08-07 06:44:43] SEQUENTIAL_SKIP: Bot #23 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:43] WEBHOOK_RECEIVED: Bot #2 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:43
[2025-08-07 06:44:43] SEQUENTIAL_SKIP: Bot #2 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:43] WEBHOOK_RECEIVED: Bot #33 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:43
[2025-08-07 06:44:43] SEQUENTIAL_SKIP: Bot #33 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:43] WEBHOOK_RECEIVED: Bot #3 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:43
[2025-08-07 06:44:43] SEQUENTIAL_SKIP: Bot #3 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:43] WEBHOOK_RECEIVED: Bot #1 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:43
[2025-08-07 06:44:43] SEQUENTIAL_START: Bot #1 starting sequential processing for Channel: -1002888471861, Message: 268
[2025-08-07 06:44:43] QUEUE_CREATE_START: Bot #1 creating queue for Channel: -1002888471861, Message: 268
[2025-08-07 06:44:43] QUEUE_CREATE_SUCCESS: Added 43 reactions for Channel: -1002888471861, Message: 268, Duration: 860s
[2025-08-07 06:44:43] WEBHOOK_RECEIVED: Bot #4 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:43
[2025-08-07 06:44:43] SEQUENTIAL_SKIP: Bot #4 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:44] WEBHOOK_RECEIVED: Bot #40 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:44
[2025-08-07 06:44:44] SEQUENTIAL_SKIP: Bot #40 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:44] WEBHOOK_RECEIVED: Bot #11 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:44
[2025-08-07 06:44:44] SEQUENTIAL_SKIP: Bot #11 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:44] WEBHOOK_RECEIVED: Bot #9 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:44
[2025-08-07 06:44:44] SEQUENTIAL_SKIP: Bot #9 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:44] WEBHOOK_RECEIVED: Bot #10 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:44
[2025-08-07 06:44:44] SEQUENTIAL_SKIP: Bot #10 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:44] WEBHOOK_RECEIVED: Bot #15 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:44
[2025-08-07 06:44:44] SEQUENTIAL_SKIP: Bot #15 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:44] WEBHOOK_RECEIVED: Bot #37 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:44
[2025-08-07 06:44:44] SEQUENTIAL_SKIP: Bot #37 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:44] WEBHOOK_RECEIVED: Bot #13 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:44
[2025-08-07 06:44:44] SEQUENTIAL_SKIP: Bot #13 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:44] WEBHOOK_RECEIVED: Bot #39 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:44
[2025-08-07 06:44:44] SEQUENTIAL_SKIP: Bot #39 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:44] WEBHOOK_RECEIVED: Bot #42 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:44
[2025-08-07 06:44:44] SEQUENTIAL_SKIP: Bot #42 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:44] WEBHOOK_RECEIVED: Bot #38 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:44
[2025-08-07 06:44:44] SEQUENTIAL_SKIP: Bot #38 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:44] WEBHOOK_RECEIVED: Bot #12 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:44
[2025-08-07 06:44:44] SEQUENTIAL_SKIP: Bot #12 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:44] WEBHOOK_RECEIVED: Bot #7 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:44
[2025-08-07 06:44:44] SEQUENTIAL_SKIP: Bot #7 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:44] WEBHOOK_RECEIVED: Bot #36 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:44
[2025-08-07 06:44:44] SEQUENTIAL_SKIP: Bot #36 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:44] WEBHOOK_RECEIVED: Bot #35 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:44
[2025-08-07 06:44:44] SEQUENTIAL_SKIP: Bot #35 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:44] WEBHOOK_RECEIVED: Bot #17 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:44
[2025-08-07 06:44:44] SEQUENTIAL_SKIP: Bot #17 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:44] WEBHOOK_RECEIVED: Bot #16 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:44
[2025-08-07 06:44:44] SEQUENTIAL_SKIP: Bot #16 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:44] WEBHOOK_RECEIVED: Bot #8 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:44
[2025-08-07 06:44:44] SEQUENTIAL_SKIP: Bot #8 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:44] WEBHOOK_RECEIVED: Bot #44 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:44
[2025-08-07 06:44:44] SEQUENTIAL_SKIP: Bot #44 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:44] WEBHOOK_RECEIVED: Bot #6 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:44
[2025-08-07 06:44:44] SEQUENTIAL_SKIP: Bot #6 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:44] WEBHOOK_RECEIVED: Bot #14 received message from Channel: -1002888471861, Message: 268, Timestamp: 2025-08-07 06:44:44
[2025-08-07 06:44:44] SEQUENTIAL_SKIP: Bot #14 skipping immediate reaction for Channel: -1002888471861, Message: 268 (will process via queue)
[2025-08-07 06:44:44] SEQUENTIAL_BOT1_SUCCESS: Bot #1 → '🥰' (Channel: -1002888471861, Message: 268) - IMMEDIATE
[2025-08-07 06:45:04] QUEUE_SUCCESS: Bot #2 → '😘' (Channel: -1002888471861, Message: 266, Delay: 30s)
[2025-08-07 06:45:05] QUEUE_SUCCESS: Bot #2 → '🤩' (Channel: -1002888471861, Message: 268, Delay: 20s)
[2025-08-07 06:45:05] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 84 remaining
[2025-08-07 06:45:05] POST_STATS: -1002888471861_266 - 1 successful, 0 errors
[2025-08-07 06:45:05] POST_STATS: -1002888471861_268 - 1 successful, 0 errors
[2025-08-07 06:46:07] QUEUE_SUCCESS: Bot #3 → '💯' (Channel: -1002888471861, Message: 266, Delay: 93s)
[2025-08-07 06:46:07] QUEUE_ERROR: Bot #3 → '🎉' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 06:46:08] QUEUE_SUCCESS: Bot #4 → '💯' (Channel: -1002888471861, Message: 266, Delay: 93s)
[2025-08-07 06:46:09] QUEUE_SUCCESS: Bot #4 → '🏆' (Channel: -1002888471861, Message: 268, Delay: 83s)
[2025-08-07 06:46:10] QUEUE_SUCCESS: Bot #5 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 93s)
[2025-08-07 06:46:11] QUEUE_SUCCESS: Bot #5 → '🏆' (Channel: -1002888471861, Message: 268, Delay: 83s)
[2025-08-07 06:46:11] QUEUE_SUMMARY: Processed 5 reactions, 1 errors, 78 remaining
[2025-08-07 06:46:11] POST_STATS: -1002888471861_266 - 3 successful, 0 errors
[2025-08-07 06:46:11] POST_STATS: -1002888471861_268 - 2 successful, 1 errors
[2025-08-07 06:47:04] QUEUE_ERROR: Bot #6 → '🎉' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 06:47:04] QUEUE_ERROR: Bot #6 → '🎉' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 06:47:05] QUEUE_SUCCESS: Bot #7 → '🥰' (Channel: -1002888471861, Message: 266, Delay: 150s)
[2025-08-07 06:47:07] QUEUE_SUCCESS: Bot #7 → '❤️' (Channel: -1002888471861, Message: 268, Delay: 140s)
[2025-08-07 06:47:07] QUEUE_SUCCESS: Bot #8 → '❤️' (Channel: -1002888471861, Message: 266, Delay: 150s)
[2025-08-07 06:47:08] QUEUE_SUCCESS: Bot #8 → '🏆' (Channel: -1002888471861, Message: 268, Delay: 140s)
[2025-08-07 06:47:08] QUEUE_SUMMARY: Processed 4 reactions, 2 errors, 72 remaining
[2025-08-07 06:47:08] POST_STATS: -1002888471861_266 - 2 successful, 1 errors
[2025-08-07 06:47:08] POST_STATS: -1002888471861_268 - 2 successful, 1 errors
[2025-08-07 06:48:05] QUEUE_SUCCESS: Bot #9 → '😘' (Channel: -1002888471861, Message: 266, Delay: 212s)
[2025-08-07 06:48:06] QUEUE_SUCCESS: Bot #9 → '🏆' (Channel: -1002888471861, Message: 268, Delay: 202s)
[2025-08-07 06:48:07] QUEUE_SUCCESS: Bot #10 → '🏆' (Channel: -1002888471861, Message: 266, Delay: 212s)
[2025-08-07 06:48:08] QUEUE_SUCCESS: Bot #10 → '🏆' (Channel: -1002888471861, Message: 268, Delay: 202s)
[2025-08-07 06:48:09] QUEUE_SUCCESS: Bot #11 → '❤️' (Channel: -1002888471861, Message: 266, Delay: 212s)
[2025-08-07 06:48:10] QUEUE_SUCCESS: Bot #11 → '🏆' (Channel: -1002888471861, Message: 268, Delay: 202s)
[2025-08-07 06:48:10] QUEUE_SUMMARY: Processed 6 reactions, 0 errors, 66 remaining
[2025-08-07 06:48:10] POST_STATS: -1002888471861_266 - 3 successful, 0 errors
[2025-08-07 06:48:10] POST_STATS: -1002888471861_268 - 3 successful, 0 errors
[2025-08-07 06:49:05] QUEUE_SUCCESS: Bot #12 → '🏆' (Channel: -1002888471861, Message: 266, Delay: 271s)
[2025-08-07 06:49:05] QUEUE_SUCCESS: Bot #12 → '❤️' (Channel: -1002888471861, Message: 268, Delay: 261s)
[2025-08-07 06:49:06] QUEUE_SUCCESS: Bot #13 → '🏆' (Channel: -1002888471861, Message: 266, Delay: 271s)
[2025-08-07 06:49:07] QUEUE_SUCCESS: Bot #13 → '💯' (Channel: -1002888471861, Message: 268, Delay: 261s)
[2025-08-07 06:49:08] QUEUE_SUCCESS: Bot #14 → '🥰' (Channel: -1002888471861, Message: 266, Delay: 271s)
[2025-08-07 06:49:09] QUEUE_SUCCESS: Bot #14 → '🤩' (Channel: -1002888471861, Message: 268, Delay: 261s)
[2025-08-07 06:49:09] QUEUE_SUMMARY: Processed 6 reactions, 0 errors, 60 remaining
[2025-08-07 06:49:09] POST_STATS: -1002888471861_266 - 3 successful, 0 errors
[2025-08-07 06:49:09] POST_STATS: -1002888471861_268 - 3 successful, 0 errors
[2025-08-07 06:49:32] QUEUE_SUCCESS: Bot #15 → '💯' (Channel: -1002888471861, Message: 266, Delay: 298s)
[2025-08-07 06:49:32] QUEUE_SUCCESS: Bot #15 → '💯' (Channel: -1002888471861, Message: 266, Delay: 298s)
[2025-08-07 06:49:32] QUEUE_SUCCESS: Bot #15 → '💯' (Channel: -1002888471861, Message: 266, Delay: 298s)
[2025-08-07 06:49:32] QUEUE_SUCCESS: Bot #15 → '💯' (Channel: -1002888471861, Message: 266, Delay: 298s)
[2025-08-07 06:49:32] QUEUE_SUCCESS: Bot #15 → '💯' (Channel: -1002888471861, Message: 266, Delay: 298s)
[2025-08-07 06:49:37] QUEUE_SUCCESS: Bot #15 → '💯' (Channel: -1002888471861, Message: 266, Delay: 298s)
[2025-08-07 06:49:37] QUEUE_SUCCESS: Bot #15 → '💯' (Channel: -1002888471861, Message: 266, Delay: 298s)
[2025-08-07 06:49:37] QUEUE_SUCCESS: Bot #15 → '💯' (Channel: -1002888471861, Message: 266, Delay: 298s)
[2025-08-07 06:49:37] QUEUE_SUCCESS: Bot #15 → '💯' (Channel: -1002888471861, Message: 266, Delay: 298s)
[2025-08-07 06:49:37] QUEUE_SUCCESS: Bot #15 → '💯' (Channel: -1002888471861, Message: 266, Delay: 298s)
[2025-08-07 06:49:37] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:37] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:37] QUEUE_ERROR: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:37] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 58 remaining
[2025-08-07 06:49:37] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:37] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:37] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:37] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:37] QUEUE_ERROR: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:37] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:37] QUEUE_ERROR: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:37] QUEUE_ERROR: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:37] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:37] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 58 remaining
[2025-08-07 06:49:37] QUEUE_ERROR: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:37] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 58 remaining
[2025-08-07 06:49:37] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:37] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 58 remaining
[2025-08-07 06:49:37] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 58 remaining
[2025-08-07 06:49:37] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:37] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:37] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:37] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:37] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:37] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:37] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:37] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:37] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:37] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:43] QUEUE_SUCCESS: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 288s)
[2025-08-07 06:49:43] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 58 remaining
[2025-08-07 06:49:43] QUEUE_SUCCESS: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 288s)
[2025-08-07 06:49:43] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 58 remaining
[2025-08-07 06:49:43] QUEUE_SUCCESS: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 288s)
[2025-08-07 06:49:43] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 58 remaining
[2025-08-07 06:49:43] QUEUE_SUCCESS: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 288s)
[2025-08-07 06:49:43] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 58 remaining
[2025-08-07 06:49:43] QUEUE_ERROR: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:43] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 58 remaining
[2025-08-07 06:49:43] QUEUE_ERROR: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:43] QUEUE_ERROR: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:43] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 58 remaining
[2025-08-07 06:49:43] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 58 remaining
[2025-08-07 06:49:43] QUEUE_SUCCESS: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 288s)
[2025-08-07 06:49:43] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 58 remaining
[2025-08-07 06:49:43] QUEUE_ERROR: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:43] QUEUE_ERROR: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:43] QUEUE_ERROR: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:43] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 58 remaining
[2025-08-07 06:49:43] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 58 remaining
[2025-08-07 06:49:43] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 58 remaining
[2025-08-07 06:49:43] QUEUE_ERROR: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:43] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 58 remaining
[2025-08-07 06:49:43] QUEUE_ERROR: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:43] QUEUE_ERROR: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:43] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 58 remaining
[2025-08-07 06:49:43] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 58 remaining
[2025-08-07 06:49:43] QUEUE_ERROR: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:43] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 58 remaining
[2025-08-07 06:49:43] QUEUE_ERROR: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:43] QUEUE_ERROR: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:43] QUEUE_ERROR: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:43] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 58 remaining
[2025-08-07 06:49:43] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 58 remaining
[2025-08-07 06:49:43] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 58 remaining
[2025-08-07 06:49:43] QUEUE_ERROR: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:43] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 58 remaining
[2025-08-07 06:49:43] QUEUE_ERROR: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:43] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 58 remaining
[2025-08-07 06:49:43] QUEUE_ERROR: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:43] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 58 remaining
[2025-08-07 06:49:43] QUEUE_ERROR: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:43] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 58 remaining
[2025-08-07 06:49:43] QUEUE_ERROR: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:43] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 58 remaining
[2025-08-07 06:49:43] QUEUE_ERROR: Bot #15 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:49:43] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 58 remaining
[2025-08-07 06:50:05] QUEUE_SUCCESS: Bot #16 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 331s)
[2025-08-07 06:50:06] QUEUE_SUCCESS: Bot #16 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 321s)
[2025-08-07 06:50:06] QUEUE_SUCCESS: Bot #17 → '😘' (Channel: -1002888471861, Message: 266, Delay: 331s)
[2025-08-07 06:50:07] QUEUE_ERROR: Bot #17 → '🎉' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 06:50:07] QUEUE_SUMMARY: Processed 3 reactions, 1 errors, 54 remaining
[2025-08-07 06:50:07] POST_STATS: -1002888471861_266 - 2 successful, 0 errors
[2025-08-07 06:50:07] POST_STATS: -1002888471861_268 - 1 successful, 1 errors
[2025-08-07 06:50:41] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:41] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:41] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:41] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:41] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:41] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:41] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:41] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:41] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:41] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:42] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:43] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:43] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:43] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:43] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:43] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:43] QUEUE_ERROR: Bot #18 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:50:43] QUEUE_SUCCESS: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266, Delay: 367s)
[2025-08-07 06:50:43] QUEUE_SUMMARY: Processed 1 reactions, 2 errors, 51 remaining
[2025-08-07 06:50:43] QUEUE_SUCCESS: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266, Delay: 367s)
[2025-08-07 06:50:43] QUEUE_SUMMARY: Processed 1 reactions, 2 errors, 51 remaining
[2025-08-07 06:50:43] QUEUE_SUCCESS: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266, Delay: 367s)
[2025-08-07 06:50:43] QUEUE_SUMMARY: Processed 1 reactions, 2 errors, 51 remaining
[2025-08-07 06:50:43] QUEUE_SUCCESS: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266, Delay: 367s)
[2025-08-07 06:50:43] QUEUE_SUMMARY: Processed 1 reactions, 2 errors, 51 remaining
[2025-08-07 06:50:43] QUEUE_SUCCESS: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266, Delay: 367s)
[2025-08-07 06:50:43] QUEUE_SUMMARY: Processed 1 reactions, 2 errors, 51 remaining
[2025-08-07 06:50:48] QUEUE_SUCCESS: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266, Delay: 367s)
[2025-08-07 06:50:48] QUEUE_SUMMARY: Processed 1 reactions, 2 errors, 51 remaining
[2025-08-07 06:50:48] QUEUE_SUCCESS: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266, Delay: 367s)
[2025-08-07 06:50:48] QUEUE_SUMMARY: Processed 1 reactions, 2 errors, 51 remaining
[2025-08-07 06:50:48] QUEUE_SUCCESS: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266, Delay: 367s)
[2025-08-07 06:50:48] QUEUE_SUMMARY: Processed 1 reactions, 2 errors, 51 remaining
[2025-08-07 06:50:48] QUEUE_SUCCESS: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266, Delay: 367s)
[2025-08-07 06:50:48] QUEUE_SUMMARY: Processed 1 reactions, 2 errors, 51 remaining
[2025-08-07 06:50:48] QUEUE_SUCCESS: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266, Delay: 367s)
[2025-08-07 06:50:48] QUEUE_SUMMARY: Processed 1 reactions, 2 errors, 51 remaining
[2025-08-07 06:50:48] QUEUE_ERROR: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:50:48] QUEUE_ERROR: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:50:48] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 51 remaining
[2025-08-07 06:50:48] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 51 remaining
[2025-08-07 06:50:48] QUEUE_ERROR: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:50:48] QUEUE_ERROR: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:50:48] QUEUE_ERROR: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:50:48] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 51 remaining
[2025-08-07 06:50:48] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 51 remaining
[2025-08-07 06:50:48] QUEUE_ERROR: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:50:48] QUEUE_ERROR: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:50:48] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 51 remaining
[2025-08-07 06:50:48] QUEUE_ERROR: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:50:48] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 51 remaining
[2025-08-07 06:50:48] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 51 remaining
[2025-08-07 06:50:48] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 51 remaining
[2025-08-07 06:50:48] QUEUE_ERROR: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:50:48] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 51 remaining
[2025-08-07 06:50:48] QUEUE_ERROR: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:50:48] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 51 remaining
[2025-08-07 06:50:48] QUEUE_ERROR: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:50:48] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 51 remaining
[2025-08-07 06:50:48] QUEUE_ERROR: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:50:48] QUEUE_ERROR: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:50:48] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 51 remaining
[2025-08-07 06:50:48] QUEUE_ERROR: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:50:48] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 51 remaining
[2025-08-07 06:50:48] QUEUE_ERROR: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:50:48] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 51 remaining
[2025-08-07 06:50:48] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 51 remaining
[2025-08-07 06:50:48] QUEUE_ERROR: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:50:48] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 51 remaining
[2025-08-07 06:50:48] QUEUE_ERROR: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:50:48] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 51 remaining
[2025-08-07 06:50:49] QUEUE_ERROR: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:50:49] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 51 remaining
[2025-08-07 06:50:49] QUEUE_ERROR: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:50:49] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 51 remaining
[2025-08-07 06:50:49] QUEUE_ERROR: Bot #19 → '🥰' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:50:49] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 51 remaining
[2025-08-07 06:51:05] QUEUE_SUCCESS: Bot #19 → '❤️' (Channel: -1002888471861, Message: 268, Delay: 381s)
[2025-08-07 06:51:06] QUEUE_SUCCESS: Bot #20 → '🏆' (Channel: -1002888471861, Message: 266, Delay: 391s)
[2025-08-07 06:51:07] QUEUE_SUCCESS: Bot #20 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 381s)
[2025-08-07 06:51:07] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 48 remaining
[2025-08-07 06:51:07] POST_STATS: -1002888471861_268 - 2 successful, 0 errors
[2025-08-07 06:51:07] POST_STATS: -1002888471861_266 - 1 successful, 0 errors
[2025-08-07 06:51:55] QUEUE_SUCCESS: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266, Delay: 441s)
[2025-08-07 06:51:55] QUEUE_SUCCESS: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266, Delay: 441s)
[2025-08-07 06:51:55] QUEUE_SUCCESS: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266, Delay: 441s)
[2025-08-07 06:51:55] QUEUE_SUCCESS: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266, Delay: 441s)
[2025-08-07 06:51:55] QUEUE_SUCCESS: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266, Delay: 441s)
[2025-08-07 06:52:00] QUEUE_SUCCESS: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266, Delay: 441s)
[2025-08-07 06:52:00] QUEUE_SUCCESS: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266, Delay: 441s)
[2025-08-07 06:52:00] QUEUE_SUCCESS: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266, Delay: 441s)
[2025-08-07 06:52:00] QUEUE_SUCCESS: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266, Delay: 441s)
[2025-08-07 06:52:00] QUEUE_SUCCESS: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266, Delay: 441s)
[2025-08-07 06:52:00] QUEUE_ERROR: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:00] QUEUE_ERROR: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:00] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:00] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:00] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:00] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:00] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:00] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:00] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:00] QUEUE_ERROR: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:00] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:00] QUEUE_ERROR: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:00] QUEUE_ERROR: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:00] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:00] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:00] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:00] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:00] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:01] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:01] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:01] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:01] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:01] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:01] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:01] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:01] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:01] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:01] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:02] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:02] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:02] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:02] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:02] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:03] QUEUE_SUCCESS: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 441s)
[2025-08-07 06:52:03] QUEUE_SUMMARY: Processed 2 reactions, 3 errors, 43 remaining
[2025-08-07 06:52:03] QUEUE_SUCCESS: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 441s)
[2025-08-07 06:52:03] QUEUE_SUMMARY: Processed 2 reactions, 3 errors, 43 remaining
[2025-08-07 06:52:03] QUEUE_SUCCESS: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 441s)
[2025-08-07 06:52:03] QUEUE_SUMMARY: Processed 2 reactions, 3 errors, 43 remaining
[2025-08-07 06:52:03] QUEUE_SUCCESS: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 441s)
[2025-08-07 06:52:03] QUEUE_SUMMARY: Processed 2 reactions, 3 errors, 43 remaining
[2025-08-07 06:52:03] QUEUE_SUCCESS: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 441s)
[2025-08-07 06:52:03] QUEUE_SUMMARY: Processed 2 reactions, 3 errors, 43 remaining
[2025-08-07 06:52:05] QUEUE_SUCCESS: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268, Delay: 431s)
[2025-08-07 06:52:06] QUEUE_SUCCESS: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268, Delay: 431s)
[2025-08-07 06:52:06] QUEUE_SUCCESS: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268, Delay: 431s)
[2025-08-07 06:52:06] QUEUE_SUCCESS: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268, Delay: 431s)
[2025-08-07 06:52:06] QUEUE_SUCCESS: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268, Delay: 432s)
[2025-08-07 06:52:06] QUEUE_ERROR: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:06] QUEUE_ERROR: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:06] QUEUE_ERROR: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:06] QUEUE_ERROR: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:06] QUEUE_ERROR: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:06] QUEUE_ERROR: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:06] QUEUE_ERROR: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:06] QUEUE_ERROR: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:06] QUEUE_ERROR: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:06] QUEUE_ERROR: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:06] QUEUE_ERROR: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:06] QUEUE_ERROR: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:06] QUEUE_ERROR: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:06] QUEUE_ERROR: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:06] QUEUE_ERROR: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:06] QUEUE_ERROR: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:06] QUEUE_ERROR: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:06] QUEUE_ERROR: Bot #21 → '🏆' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:06] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:06] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:07] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:07] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:07] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:07] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:07] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:07] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:07] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:07] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:07] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:07] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:07] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:07] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:07] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:07] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:07] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:07] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:07] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:07] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:07] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:07] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:07] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:07] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:07] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:08] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:08] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:08] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:08] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:08] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:08] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:08] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:08] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:08] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:08] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:08] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:08] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:08] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:08] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:08] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:08] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:08] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:08] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:08] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:08] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:08] QUEUE_ERROR: Bot #22 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:52:08] QUEUE_SUCCESS: Bot #23 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 441s)
[2025-08-07 06:52:08] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 42 remaining
[2025-08-07 06:52:08] POST_STATS: -1002888471861_268 - 1 successful, 0 errors
[2025-08-07 06:52:09] QUEUE_SUCCESS: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 441s)
[2025-08-07 06:52:09] QUEUE_SUMMARY: Processed 3 reactions, 2 errors, 43 remaining
[2025-08-07 06:52:09] QUEUE_SUCCESS: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 441s)
[2025-08-07 06:52:09] QUEUE_SUMMARY: Processed 3 reactions, 2 errors, 43 remaining
[2025-08-07 06:52:09] QUEUE_SUCCESS: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 442s)
[2025-08-07 06:52:09] QUEUE_SUMMARY: Processed 1 reactions, 4 errors, 43 remaining
[2025-08-07 06:52:09] QUEUE_SUCCESS: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 442s)
[2025-08-07 06:52:09] QUEUE_SUMMARY: Processed 1 reactions, 4 errors, 43 remaining
[2025-08-07 06:52:14] QUEUE_SUCCESS: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 441s)
[2025-08-07 06:52:14] QUEUE_SUMMARY: Processed 3 reactions, 2 errors, 43 remaining
[2025-08-07 06:52:14] QUEUE_SUCCESS: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 442s)
[2025-08-07 06:52:14] QUEUE_SUMMARY: Processed 1 reactions, 4 errors, 43 remaining
[2025-08-07 06:52:14] QUEUE_SUCCESS: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 442s)
[2025-08-07 06:52:14] QUEUE_SUMMARY: Processed 1 reactions, 4 errors, 43 remaining
[2025-08-07 06:52:14] QUEUE_SUCCESS: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 442s)
[2025-08-07 06:52:14] QUEUE_SUMMARY: Processed 1 reactions, 4 errors, 43 remaining
[2025-08-07 06:52:14] QUEUE_SUCCESS: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 442s)
[2025-08-07 06:52:14] QUEUE_SUMMARY: Processed 2 reactions, 3 errors, 43 remaining
[2025-08-07 06:52:14] QUEUE_ERROR: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:14] QUEUE_ERROR: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:14] QUEUE_SUMMARY: Processed 0 reactions, 5 errors, 43 remaining
[2025-08-07 06:52:14] QUEUE_SUMMARY: Processed 0 reactions, 5 errors, 43 remaining
[2025-08-07 06:52:14] QUEUE_ERROR: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:14] QUEUE_ERROR: Bot #23 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:14] QUEUE_ERROR: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:14] QUEUE_SUMMARY: Processed 0 reactions, 5 errors, 43 remaining
[2025-08-07 06:52:14] QUEUE_ERROR: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:14] QUEUE_SUMMARY: Processed 0 reactions, 5 errors, 43 remaining
[2025-08-07 06:52:14] QUEUE_ERROR: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:14] QUEUE_SUMMARY: Processed 0 reactions, 5 errors, 43 remaining
[2025-08-07 06:52:14] QUEUE_SUMMARY: Processed 0 reactions, 5 errors, 43 remaining
[2025-08-07 06:52:14] QUEUE_ERROR: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:14] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 42 remaining
[2025-08-07 06:52:14] QUEUE_SUMMARY: Processed 0 reactions, 5 errors, 43 remaining
[2025-08-07 06:52:14] QUEUE_ERROR: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:14] QUEUE_SUMMARY: Processed 0 reactions, 5 errors, 43 remaining
[2025-08-07 06:52:14] QUEUE_ERROR: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:14] QUEUE_SUMMARY: Processed 0 reactions, 5 errors, 43 remaining
[2025-08-07 06:52:14] QUEUE_ERROR: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:14] QUEUE_SUMMARY: Processed 0 reactions, 5 errors, 43 remaining
[2025-08-07 06:52:14] QUEUE_ERROR: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:14] QUEUE_SUMMARY: Processed 0 reactions, 5 errors, 43 remaining
[2025-08-07 06:52:14] QUEUE_ERROR: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:14] QUEUE_SUMMARY: Processed 0 reactions, 5 errors, 43 remaining
[2025-08-07 06:52:14] QUEUE_ERROR: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:14] QUEUE_SUMMARY: Processed 1 reactions, 4 errors, 43 remaining
[2025-08-07 06:52:14] QUEUE_ERROR: Bot #23 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:52:14] QUEUE_SUMMARY: Processed 2 reactions, 3 errors, 43 remaining
[2025-08-07 06:52:41] QUEUE_SUCCESS: Bot #23 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 477s)
[2025-08-07 06:52:42] QUEUE_ERROR: Bot #24 → '🎉' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 06:52:43] QUEUE_SUCCESS: Bot #24 → '❤️' (Channel: -1002888471861, Message: 268, Delay: 477s)
[2025-08-07 06:52:44] QUEUE_SUCCESS: Bot #25 → '😘' (Channel: -1002888471861, Message: 266, Delay: 487s)
[2025-08-07 06:52:44] QUEUE_SUMMARY: Processed 3 reactions, 1 errors, 39 remaining
[2025-08-07 06:53:05] QUEUE_SUCCESS: Bot #25 → '❤️' (Channel: -1002888471861, Message: 268, Delay: 501s)
[2025-08-07 06:53:06] QUEUE_ERROR: Bot #26 → '😘' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:53:06] QUEUE_ERROR: Bot #26 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:53:06] QUEUE_SUMMARY: Processed 1 reactions, 2 errors, 36 remaining
[2025-08-07 06:53:06] POST_STATS: -1002888471861_268 - 1 successful, 1 errors
[2025-08-07 06:53:06] POST_STATS: -1002888471861_266 - 0 successful, 1 errors
[2025-08-07 06:54:06] QUEUE_ERROR: Bot #27 → '❤️' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:54:07] QUEUE_ERROR: Bot #27 → '🏆' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:54:08] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 266) - HTTP 401: Unauthorized
[2025-08-07 06:54:08] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 401: Unauthorized
[2025-08-07 06:54:09] QUEUE_ERROR: Bot #29 → '😘' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:54:10] QUEUE_ERROR: Bot #29 → '🎉' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:54:10] QUEUE_SUMMARY: Processed 0 reactions, 6 errors, 30 remaining
[2025-08-07 06:54:10] POST_STATS: -1002888471861_266 - 0 successful, 3 errors
[2025-08-07 06:54:10] POST_STATS: -1002888471861_268 - 0 successful, 3 errors
[2025-08-07 06:55:05] QUEUE_SUCCESS: Bot #30 → '🏆' (Channel: -1002888471861, Message: 266, Delay: 631s)
[2025-08-07 06:55:06] QUEUE_SUCCESS: Bot #30 → '😘' (Channel: -1002888471861, Message: 268, Delay: 621s)
[2025-08-07 06:55:07] QUEUE_ERROR: Bot #31 → '🎉' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:55:08] QUEUE_ERROR: Bot #31 → '🤩' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:55:09] QUEUE_ERROR: Bot #32 → '🎉' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:55:10] QUEUE_ERROR: Bot #32 → '🎉' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: chat not found
[2025-08-07 06:55:10] QUEUE_SUMMARY: Processed 2 reactions, 4 errors, 24 remaining
[2025-08-07 06:55:10] POST_STATS: -1002888471861_266 - 1 successful, 2 errors
[2025-08-07 06:55:10] POST_STATS: -1002888471861_268 - 1 successful, 2 errors
[2025-08-07 06:55:40] QUEUE_SUCCESS: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 666s)
[2025-08-07 06:55:40] QUEUE_SUCCESS: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 666s)
[2025-08-07 06:55:40] QUEUE_SUCCESS: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 666s)
[2025-08-07 06:55:40] QUEUE_SUCCESS: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 666s)
[2025-08-07 06:55:40] QUEUE_SUCCESS: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 666s)
[2025-08-07 06:55:45] QUEUE_SUCCESS: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 666s)
[2025-08-07 06:55:45] QUEUE_SUCCESS: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 666s)
[2025-08-07 06:55:45] QUEUE_SUCCESS: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 666s)
[2025-08-07 06:55:45] QUEUE_SUCCESS: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 666s)
[2025-08-07 06:55:45] QUEUE_ERROR: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:45] QUEUE_ERROR: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:45] QUEUE_ERROR: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:45] QUEUE_ERROR: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:45] QUEUE_ERROR: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:45] QUEUE_ERROR: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:45] QUEUE_ERROR: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:45] QUEUE_ERROR: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:45] QUEUE_ERROR: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:45] QUEUE_ERROR: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:45] QUEUE_ERROR: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:45] QUEUE_ERROR: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:45] QUEUE_ERROR: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:45] QUEUE_ERROR: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:45] QUEUE_SUCCESS: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 666s)
[2025-08-07 06:55:45] QUEUE_ERROR: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:45] QUEUE_ERROR: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:45] QUEUE_ERROR: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:45] QUEUE_ERROR: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:45] QUEUE_ERROR: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:45] QUEUE_ERROR: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:45] QUEUE_ERROR: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:45] QUEUE_ERROR: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:45] QUEUE_ERROR: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:45] QUEUE_ERROR: Bot #33 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:46] QUEUE_SUCCESS: Bot #34 → '😘' (Channel: -1002888471861, Message: 266, Delay: 666s)
[2025-08-07 06:55:46] QUEUE_SUMMARY: Processed 2 reactions, 1 errors, 21 remaining
[2025-08-07 06:55:46] QUEUE_SUCCESS: Bot #34 → '😘' (Channel: -1002888471861, Message: 266, Delay: 666s)
[2025-08-07 06:55:46] QUEUE_SUMMARY: Processed 2 reactions, 1 errors, 21 remaining
[2025-08-07 06:55:46] QUEUE_SUCCESS: Bot #34 → '😘' (Channel: -1002888471861, Message: 266, Delay: 666s)
[2025-08-07 06:55:46] QUEUE_SUMMARY: Processed 2 reactions, 1 errors, 21 remaining
[2025-08-07 06:55:46] QUEUE_SUCCESS: Bot #34 → '😘' (Channel: -1002888471861, Message: 266, Delay: 666s)
[2025-08-07 06:55:46] QUEUE_SUMMARY: Processed 2 reactions, 1 errors, 21 remaining
[2025-08-07 06:55:46] QUEUE_SUCCESS: Bot #34 → '😘' (Channel: -1002888471861, Message: 266, Delay: 666s)
[2025-08-07 06:55:46] QUEUE_SUMMARY: Processed 2 reactions, 1 errors, 21 remaining
[2025-08-07 06:55:50] QUEUE_SUCCESS: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268, Delay: 656s)
[2025-08-07 06:55:50] QUEUE_SUCCESS: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268, Delay: 656s)
[2025-08-07 06:55:50] QUEUE_SUCCESS: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268, Delay: 656s)
[2025-08-07 06:55:50] QUEUE_SUCCESS: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268, Delay: 656s)
[2025-08-07 06:55:50] QUEUE_ERROR: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:50] QUEUE_ERROR: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:50] QUEUE_ERROR: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:50] QUEUE_ERROR: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:50] QUEUE_ERROR: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:50] QUEUE_ERROR: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:50] QUEUE_ERROR: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:50] QUEUE_ERROR: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:50] QUEUE_ERROR: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:50] QUEUE_ERROR: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:50] QUEUE_SUCCESS: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268, Delay: 656s)
[2025-08-07 06:55:50] QUEUE_ERROR: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:50] QUEUE_ERROR: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:50] QUEUE_ERROR: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:50] QUEUE_ERROR: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:50] QUEUE_ERROR: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:50] QUEUE_ERROR: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:50] QUEUE_ERROR: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:50] QUEUE_ERROR: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:50] QUEUE_ERROR: Bot #33 → '❤️' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:51] QUEUE_SUCCESS: Bot #34 → '😘' (Channel: -1002888471861, Message: 266, Delay: 666s)
[2025-08-07 06:55:51] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 21 remaining
[2025-08-07 06:55:51] QUEUE_SUCCESS: Bot #34 → '😘' (Channel: -1002888471861, Message: 266, Delay: 666s)
[2025-08-07 06:55:51] QUEUE_SUCCESS: Bot #34 → '😘' (Channel: -1002888471861, Message: 266, Delay: 666s)
[2025-08-07 06:55:51] QUEUE_SUMMARY: Processed 2 reactions, 1 errors, 21 remaining
[2025-08-07 06:55:51] QUEUE_SUMMARY: Processed 2 reactions, 1 errors, 21 remaining
[2025-08-07 06:55:51] QUEUE_SUCCESS: Bot #34 → '😘' (Channel: -1002888471861, Message: 266, Delay: 666s)
[2025-08-07 06:55:51] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 21 remaining
[2025-08-07 06:55:51] QUEUE_SUCCESS: Bot #34 → '😘' (Channel: -1002888471861, Message: 266, Delay: 666s)
[2025-08-07 06:55:51] QUEUE_SUMMARY: Processed 1 reactions, 2 errors, 21 remaining
[2025-08-07 06:55:56] QUEUE_SUCCESS: Bot #34 → '😘' (Channel: -1002888471861, Message: 266, Delay: 666s)
[2025-08-07 06:55:56] QUEUE_SUMMARY: Processed 1 reactions, 2 errors, 21 remaining
[2025-08-07 06:55:56] QUEUE_SUCCESS: Bot #34 → '😘' (Channel: -1002888471861, Message: 266, Delay: 666s)
[2025-08-07 06:55:56] QUEUE_SUMMARY: Processed 1 reactions, 2 errors, 21 remaining
[2025-08-07 06:55:56] QUEUE_SUCCESS: Bot #34 → '😘' (Channel: -1002888471861, Message: 266, Delay: 666s)
[2025-08-07 06:55:56] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 21 remaining
[2025-08-07 06:55:56] QUEUE_SUCCESS: Bot #34 → '😘' (Channel: -1002888471861, Message: 266, Delay: 666s)
[2025-08-07 06:55:56] QUEUE_SUMMARY: Processed 1 reactions, 2 errors, 21 remaining
[2025-08-07 06:55:56] QUEUE_SUCCESS: Bot #34 → '😘' (Channel: -1002888471861, Message: 266, Delay: 666s)
[2025-08-07 06:55:56] QUEUE_SUMMARY: Processed 1 reactions, 2 errors, 21 remaining
[2025-08-07 06:55:56] QUEUE_ERROR: Bot #34 → '😘' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:56] QUEUE_ERROR: Bot #34 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:56] QUEUE_ERROR: Bot #34 → '😘' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:56] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 21 remaining
[2025-08-07 06:55:56] QUEUE_ERROR: Bot #34 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 20 remaining
[2025-08-07 06:55:56] QUEUE_ERROR: Bot #34 → '😘' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:56] QUEUE_ERROR: Bot #34 → '😘' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:56] QUEUE_ERROR: Bot #34 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:56] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 21 remaining
[2025-08-07 06:55:56] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 21 remaining
[2025-08-07 06:55:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 20 remaining
[2025-08-07 06:55:56] QUEUE_ERROR: Bot #34 → '😘' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:56] QUEUE_ERROR: Bot #34 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:56] QUEUE_ERROR: Bot #34 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:56] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 21 remaining
[2025-08-07 06:55:56] QUEUE_ERROR: Bot #34 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:56] QUEUE_ERROR: Bot #34 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:56] QUEUE_ERROR: Bot #34 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:56] QUEUE_SUMMARY: Processed 1 reactions, 2 errors, 21 remaining
[2025-08-07 06:55:56] QUEUE_ERROR: Bot #34 → '😘' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 20 remaining
[2025-08-07 06:55:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 20 remaining
[2025-08-07 06:55:56] QUEUE_ERROR: Bot #34 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 20 remaining
[2025-08-07 06:55:56] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 21 remaining
[2025-08-07 06:55:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 20 remaining
[2025-08-07 06:55:56] QUEUE_ERROR: Bot #34 → '😘' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:56] QUEUE_ERROR: Bot #34 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:56] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 21 remaining
[2025-08-07 06:55:56] QUEUE_ERROR: Bot #34 → '💯' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:56] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 20 remaining
[2025-08-07 06:55:56] QUEUE_ERROR: Bot #34 → '😘' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:56] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 21 remaining
[2025-08-07 06:55:56] QUEUE_ERROR: Bot #34 → '😘' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:56] QUEUE_SUMMARY: Processed 1 reactions, 2 errors, 21 remaining
[2025-08-07 06:55:57] QUEUE_ERROR: Bot #34 → '😘' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:57] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 21 remaining
[2025-08-07 06:55:57] QUEUE_ERROR: Bot #34 → '😘' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:57] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 21 remaining
[2025-08-07 06:55:57] QUEUE_ERROR: Bot #34 → '😘' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:57] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 21 remaining
[2025-08-07 06:55:57] QUEUE_ERROR: Bot #34 → '😘' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:57] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 21 remaining
[2025-08-07 06:55:57] QUEUE_ERROR: Bot #34 → '😘' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:55:57] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 21 remaining
[2025-08-07 06:55:57] QUEUE_SUCCESS: Bot #35 → '❤️' (Channel: -1002888471861, Message: 266, Delay: 680s)
[2025-08-07 06:55:57] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 19 remaining
[2025-08-07 06:55:57] QUEUE_SUCCESS: Bot #35 → '❤️' (Channel: -1002888471861, Message: 266, Delay: 680s)
[2025-08-07 06:55:57] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 19 remaining
[2025-08-07 06:55:57] QUEUE_SUCCESS: Bot #35 → '❤️' (Channel: -1002888471861, Message: 266, Delay: 680s)
[2025-08-07 06:55:57] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 19 remaining
[2025-08-07 06:55:57] QUEUE_SUCCESS: Bot #35 → '❤️' (Channel: -1002888471861, Message: 266, Delay: 682s)
[2025-08-07 06:55:57] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 19 remaining
[2025-08-07 06:56:05] QUEUE_ERROR: Bot #35 → '🎉' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 06:56:05] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 18 remaining
[2025-08-07 06:56:05] POST_STATS: -1002888471861_268 - 0 successful, 1 errors
[2025-08-07 06:56:23] QUEUE_SUCCESS: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 709s)
[2025-08-07 06:56:23] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 17 remaining
[2025-08-07 06:56:23] QUEUE_SUCCESS: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 709s)
[2025-08-07 06:56:23] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 17 remaining
[2025-08-07 06:56:23] QUEUE_SUCCESS: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 709s)
[2025-08-07 06:56:23] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 17 remaining
[2025-08-07 06:56:23] QUEUE_SUCCESS: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 709s)
[2025-08-07 06:56:23] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 17 remaining
[2025-08-07 06:56:23] QUEUE_SUCCESS: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 709s)
[2025-08-07 06:56:23] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 17 remaining
[2025-08-07 06:56:28] QUEUE_SUCCESS: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 709s)
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 17 remaining
[2025-08-07 06:56:28] QUEUE_SUCCESS: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 709s)
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 17 remaining
[2025-08-07 06:56:28] QUEUE_SUCCESS: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 709s)
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 17 remaining
[2025-08-07 06:56:28] QUEUE_SUCCESS: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 709s)
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 17 remaining
[2025-08-07 06:56:28] QUEUE_ERROR: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 16 remaining
[2025-08-07 06:56:28] QUEUE_ERROR: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:56:28] QUEUE_ERROR: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:56:28] QUEUE_ERROR: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:56:28] QUEUE_ERROR: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:56:28] QUEUE_ERROR: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:56:28] QUEUE_SUCCESS: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 709s)
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 17 remaining
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 17 remaining
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 17 remaining
[2025-08-07 06:56:28] QUEUE_ERROR: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:56:28] QUEUE_ERROR: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:56:28] QUEUE_ERROR: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:56:28] QUEUE_ERROR: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 17 remaining
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 17 remaining
[2025-08-07 06:56:28] QUEUE_ERROR: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 16 remaining
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 17 remaining
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 16 remaining
[2025-08-07 06:56:28] QUEUE_ERROR: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 16 remaining
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 17 remaining
[2025-08-07 06:56:28] QUEUE_ERROR: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 17 remaining
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 17 remaining
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 16 remaining
[2025-08-07 06:56:28] QUEUE_ERROR: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:56:28] QUEUE_ERROR: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:56:28] QUEUE_ERROR: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:56:28] QUEUE_ERROR: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 17 remaining
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 17 remaining
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 17 remaining
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 17 remaining
[2025-08-07 06:56:28] QUEUE_ERROR: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 17 remaining
[2025-08-07 06:56:28] QUEUE_ERROR: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 17 remaining
[2025-08-07 06:56:28] QUEUE_ERROR: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 17 remaining
[2025-08-07 06:56:28] QUEUE_ERROR: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 17 remaining
[2025-08-07 06:56:28] QUEUE_ERROR: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 17 remaining
[2025-08-07 06:56:28] QUEUE_ERROR: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 17 remaining
[2025-08-07 06:56:28] QUEUE_ERROR: Bot #36 → '🤩' (Channel: -1002888471861, Message: 266) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:56:28] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 17 remaining
[2025-08-07 06:56:33] QUEUE_SUCCESS: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 706s)
[2025-08-07 06:56:33] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 16 remaining
[2025-08-07 06:56:33] QUEUE_SUCCESS: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 706s)
[2025-08-07 06:56:33] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 16 remaining
[2025-08-07 06:56:33] QUEUE_SUCCESS: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 706s)
[2025-08-07 06:56:33] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 16 remaining
[2025-08-07 06:56:33] QUEUE_SUCCESS: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 706s)
[2025-08-07 06:56:33] QUEUE_SUCCESS: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 706s)
[2025-08-07 06:56:33] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 16 remaining
[2025-08-07 06:56:33] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 16 remaining
[2025-08-07 06:56:38] QUEUE_SUCCESS: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 706s)
[2025-08-07 06:56:38] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 16 remaining
[2025-08-07 06:56:38] QUEUE_SUCCESS: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 706s)
[2025-08-07 06:56:38] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 16 remaining
[2025-08-07 06:56:38] QUEUE_SUCCESS: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 706s)
[2025-08-07 06:56:38] QUEUE_SUCCESS: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 706s)
[2025-08-07 06:56:38] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 16 remaining
[2025-08-07 06:56:38] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 16 remaining
[2025-08-07 06:56:38] QUEUE_SUCCESS: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 706s)
[2025-08-07 06:56:38] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 16 remaining
[2025-08-07 06:56:38] QUEUE_ERROR: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 44
[2025-08-07 06:56:38] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 16 remaining
[2025-08-07 06:56:38] QUEUE_ERROR: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 44
[2025-08-07 06:56:38] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 16 remaining
[2025-08-07 06:56:39] QUEUE_ERROR: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 44
[2025-08-07 06:56:39] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 16 remaining
[2025-08-07 06:56:39] QUEUE_ERROR: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 44
[2025-08-07 06:56:39] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 16 remaining
[2025-08-07 06:56:39] QUEUE_ERROR: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 44
[2025-08-07 06:56:39] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 16 remaining
[2025-08-07 06:56:39] QUEUE_ERROR: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 44
[2025-08-07 06:56:39] QUEUE_ERROR: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 44
[2025-08-07 06:56:39] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 16 remaining
[2025-08-07 06:56:39] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 16 remaining
[2025-08-07 06:56:39] QUEUE_ERROR: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 44
[2025-08-07 06:56:39] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 16 remaining
[2025-08-07 06:56:39] QUEUE_ERROR: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 44
[2025-08-07 06:56:39] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 16 remaining
[2025-08-07 06:56:39] QUEUE_ERROR: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 44
[2025-08-07 06:56:39] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 16 remaining
[2025-08-07 06:56:39] QUEUE_ERROR: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 44
[2025-08-07 06:56:39] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 16 remaining
[2025-08-07 06:56:39] QUEUE_ERROR: Bot #36 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 44
[2025-08-07 06:56:39] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 16 remaining
[2025-08-07 06:57:04] QUEUE_SUCCESS: Bot #37 → '💯' (Channel: -1002888471861, Message: 266, Delay: 750s)
[2025-08-07 06:57:05] QUEUE_SUCCESS: Bot #37 → '❤️' (Channel: -1002888471861, Message: 268, Delay: 740s)
[2025-08-07 06:57:06] QUEUE_SUCCESS: Bot #38 → '😘' (Channel: -1002888471861, Message: 266, Delay: 750s)
[2025-08-07 06:57:07] QUEUE_SUCCESS: Bot #38 → '💯' (Channel: -1002888471861, Message: 268, Delay: 740s)
[2025-08-07 06:57:07] QUEUE_SUMMARY: Processed 4 reactions, 0 errors, 12 remaining
[2025-08-07 06:57:07] POST_STATS: -1002888471861_266 - 2 successful, 0 errors
[2025-08-07 06:57:07] POST_STATS: -1002888471861_268 - 2 successful, 0 errors
[2025-08-07 06:57:58] QUEUE_SUCCESS: Bot #39 → '🏆' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:57:58] QUEUE_SUCCESS: Bot #39 → '🏆' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:57:58] QUEUE_SUCCESS: Bot #39 → '🏆' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:57:58] QUEUE_SUCCESS: Bot #39 → '🏆' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:57:58] QUEUE_SUCCESS: Bot #39 → '🏆' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:58:03] QUEUE_SUCCESS: Bot #39 → '🏆' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:58:03] QUEUE_SUCCESS: Bot #39 → '🏆' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:58:03] QUEUE_SUCCESS: Bot #39 → '🏆' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:58:03] QUEUE_SUCCESS: Bot #39 → '🏆' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:58:03] QUEUE_SUCCESS: Bot #39 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 794s)
[2025-08-07 06:58:03] QUEUE_ERROR: Bot #39 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:58:03] QUEUE_ERROR: Bot #39 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:58:03] QUEUE_ERROR: Bot #39 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:58:03] QUEUE_ERROR: Bot #39 → '🥰' (Channel: -1002888471861, Message: 268) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 06:58:04] QUEUE_SUCCESS: Bot #40 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:58:04] QUEUE_SUCCESS: Bot #40 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:58:04] QUEUE_SUCCESS: Bot #40 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:58:04] QUEUE_SUCCESS: Bot #40 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:58:04] QUEUE_SUCCESS: Bot #40 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:58:11] QUEUE_SUCCESS: Bot #39 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 794s)
[2025-08-07 06:58:11] QUEUE_SUCCESS: Bot #39 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 794s)
[2025-08-07 06:58:11] QUEUE_SUCCESS: Bot #39 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 794s)
[2025-08-07 06:58:11] QUEUE_SUCCESS: Bot #39 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 794s)
[2025-08-07 06:58:11] QUEUE_SUCCESS: Bot #39 → '🏆' (Channel: -1002888471861, Message: 266, Delay: 811s)
[2025-08-07 06:58:12] QUEUE_SUCCESS: Bot #40 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 794s)
[2025-08-07 06:58:12] QUEUE_SUCCESS: Bot #40 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 794s)
[2025-08-07 06:58:12] QUEUE_SUCCESS: Bot #40 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 794s)
[2025-08-07 06:58:12] QUEUE_SUCCESS: Bot #40 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 794s)
[2025-08-07 06:58:12] QUEUE_SUCCESS: Bot #40 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 794s)
[2025-08-07 06:58:13] QUEUE_SUCCESS: Bot #41 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:58:13] QUEUE_SUMMARY: Processed 4 reactions, 1 errors, 7 remaining
[2025-08-07 06:58:13] QUEUE_SUCCESS: Bot #41 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:58:13] QUEUE_SUMMARY: Processed 5 reactions, 0 errors, 7 remaining
[2025-08-07 06:58:13] QUEUE_SUCCESS: Bot #41 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:58:13] QUEUE_SUMMARY: Processed 4 reactions, 1 errors, 7 remaining
[2025-08-07 06:58:13] QUEUE_SUCCESS: Bot #41 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:58:13] QUEUE_SUMMARY: Processed 4 reactions, 1 errors, 7 remaining
[2025-08-07 06:58:13] QUEUE_SUCCESS: Bot #41 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:58:13] QUEUE_SUMMARY: Processed 4 reactions, 1 errors, 7 remaining
[2025-08-07 06:58:16] QUEUE_SUCCESS: Bot #39 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 801s)
[2025-08-07 06:58:17] QUEUE_SUCCESS: Bot #40 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:58:17] QUEUE_SUCCESS: Bot #40 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:58:17] QUEUE_SUCCESS: Bot #40 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:58:17] QUEUE_SUCCESS: Bot #40 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:58:17] QUEUE_SUCCESS: Bot #40 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 811s)
[2025-08-07 06:58:25] QUEUE_SUCCESS: Bot #40 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 794s)
[2025-08-07 06:58:25] QUEUE_SUCCESS: Bot #40 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 801s)
[2025-08-07 06:58:25] QUEUE_SUCCESS: Bot #40 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 794s)
[2025-08-07 06:58:26] QUEUE_SUCCESS: Bot #40 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 794s)
[2025-08-07 06:58:26] QUEUE_SUCCESS: Bot #40 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 794s)
[2025-08-07 06:58:26] QUEUE_SUCCESS: Bot #41 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:58:26] QUEUE_SUMMARY: Processed 5 reactions, 0 errors, 7 remaining
[2025-08-07 06:58:26] QUEUE_SUCCESS: Bot #41 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 811s)
[2025-08-07 06:58:26] QUEUE_SUCCESS: Bot #41 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:58:26] QUEUE_SUMMARY: Processed 5 reactions, 0 errors, 7 remaining
[2025-08-07 06:58:26] QUEUE_SUCCESS: Bot #41 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:58:26] QUEUE_SUMMARY: Processed 5 reactions, 0 errors, 7 remaining
[2025-08-07 06:58:26] QUEUE_SUCCESS: Bot #41 → '🤩' (Channel: -1002888471861, Message: 266, Delay: 804s)
[2025-08-07 06:58:26] QUEUE_SUMMARY: Processed 5 reactions, 0 errors, 7 remaining
[2025-08-07 06:58:31] QUEUE_ERROR: Bot #41 → '🎉' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 06:58:31] QUEUE_SUMMARY: Processed 5 reactions, 1 errors, 6 remaining
[2025-08-07 06:58:31] POST_STATS: -1002888471861_266 - 3 successful, 0 errors
[2025-08-07 06:58:31] POST_STATS: -1002888471861_268 - 2 successful, 1 errors
[2025-08-07 06:58:32] QUEUE_ERROR: Bot #41 → '🎉' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 06:58:32] QUEUE_ERROR: Bot #41 → '🎉' (Channel: -1002888471861, Message: 268) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 06:58:33] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 06:58:33] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 06:58:33] QUEUE_SUCCESS: Bot #42 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 824s)
[2025-08-07 06:58:33] QUEUE_SUMMARY: Processed 1 reactions, 2 errors, 4 remaining
[2025-08-07 06:58:34] QUEUE_SUCCESS: Bot #42 → '🥰' (Channel: -1002888471861, Message: 268, Delay: 827s)
[2025-08-07 06:58:34] QUEUE_SUMMARY: Processed 1 reactions, 2 errors, 4 remaining
[2025-08-07 06:59:04] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 266) - HTTP 401: Unauthorized
[2025-08-07 06:59:05] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 268) - HTTP 401: Unauthorized
[2025-08-07 06:59:06] QUEUE_ERROR: Bot #44 → '🎉' (Channel: -1002888471861, Message: 266) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 06:59:07] QUEUE_SUCCESS: Bot #44 → '💯' (Channel: -1002888471861, Message: 268, Delay: 861s)
[2025-08-07 06:59:07] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 0 remaining
[2025-08-07 06:59:07] POST_STATS: -1002888471861_266 - 0 successful, 2 errors
[2025-08-07 06:59:07] POST_STATS: -1002888471861_268 - 1 successful, 1 errors
[2025-08-07 07:17:31] WEBHOOK_RECEIVED: Bot #24 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:31
[2025-08-07 07:17:31] SEQUENTIAL_SKIP: Bot #24 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:31] WEBHOOK_RECEIVED: Bot #5 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:31
[2025-08-07 07:17:31] SEQUENTIAL_SKIP: Bot #5 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:31] WEBHOOK_RECEIVED: Bot #33 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:31
[2025-08-07 07:17:31] SEQUENTIAL_SKIP: Bot #33 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:31] WEBHOOK_RECEIVED: Bot #41 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:31
[2025-08-07 07:17:31] SEQUENTIAL_SKIP: Bot #41 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:31] WEBHOOK_RECEIVED: Bot #19 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:31
[2025-08-07 07:17:31] SEQUENTIAL_SKIP: Bot #19 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:31] WEBHOOK_RECEIVED: Bot #23 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:31
[2025-08-07 07:17:31] SEQUENTIAL_SKIP: Bot #23 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:31] WEBHOOK_RECEIVED: Bot #1 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:31
[2025-08-07 07:17:31] SEQUENTIAL_START: Bot #1 starting sequential processing for Channel: -1002888471861, Message: 269
[2025-08-07 07:17:31] QUEUE_CREATE_START: Bot #1 creating queue for Channel: -1002888471861, Message: 269
[2025-08-07 07:17:31] QUEUE_CREATE_SUCCESS: Added 43 reactions for Channel: -1002888471861, Message: 269, Duration: 860s
[2025-08-07 07:17:31] WEBHOOK_RECEIVED: Bot #4 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:31
[2025-08-07 07:17:31] SEQUENTIAL_SKIP: Bot #4 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:31] WEBHOOK_RECEIVED: Bot #30 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:31
[2025-08-07 07:17:31] SEQUENTIAL_SKIP: Bot #30 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:31] WEBHOOK_RECEIVED: Bot #34 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:31
[2025-08-07 07:17:31] SEQUENTIAL_SKIP: Bot #34 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:32] WEBHOOK_RECEIVED: Bot #17 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:32
[2025-08-07 07:17:32] SEQUENTIAL_SKIP: Bot #17 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:32] WEBHOOK_RECEIVED: Bot #10 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:32
[2025-08-07 07:17:32] SEQUENTIAL_SKIP: Bot #10 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:32] WEBHOOK_RECEIVED: Bot #44 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:32
[2025-08-07 07:17:32] SEQUENTIAL_SKIP: Bot #44 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:32] WEBHOOK_RECEIVED: Bot #42 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:32
[2025-08-07 07:17:32] SEQUENTIAL_SKIP: Bot #42 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:32] WEBHOOK_RECEIVED: Bot #35 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:32
[2025-08-07 07:17:32] SEQUENTIAL_SKIP: Bot #35 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:32] WEBHOOK_RECEIVED: Bot #15 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:32
[2025-08-07 07:17:32] SEQUENTIAL_SKIP: Bot #15 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:32] WEBHOOK_RECEIVED: Bot #12 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:32
[2025-08-07 07:17:32] SEQUENTIAL_SKIP: Bot #12 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:32] WEBHOOK_RECEIVED: Bot #38 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:32
[2025-08-07 07:17:32] SEQUENTIAL_SKIP: Bot #38 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:32] WEBHOOK_RECEIVED: Bot #11 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:32
[2025-08-07 07:17:32] SEQUENTIAL_SKIP: Bot #11 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:32] WEBHOOK_RECEIVED: Bot #6 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:32
[2025-08-07 07:17:32] SEQUENTIAL_SKIP: Bot #6 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:32] WEBHOOK_RECEIVED: Bot #14 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:32
[2025-08-07 07:17:32] SEQUENTIAL_SKIP: Bot #14 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:32] WEBHOOK_RECEIVED: Bot #7 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:32
[2025-08-07 07:17:32] SEQUENTIAL_SKIP: Bot #7 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:32] WEBHOOK_RECEIVED: Bot #36 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:32
[2025-08-07 07:17:32] SEQUENTIAL_SKIP: Bot #36 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:32] WEBHOOK_RECEIVED: Bot #3 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:32
[2025-08-07 07:17:32] SEQUENTIAL_SKIP: Bot #3 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:32] WEBHOOK_RECEIVED: Bot #25 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:32
[2025-08-07 07:17:32] SEQUENTIAL_SKIP: Bot #25 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:32] WEBHOOK_RECEIVED: Bot #20 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:32
[2025-08-07 07:17:32] SEQUENTIAL_SKIP: Bot #20 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:32] WEBHOOK_RECEIVED: Bot #39 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:32
[2025-08-07 07:17:32] SEQUENTIAL_SKIP: Bot #39 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:32] WEBHOOK_RECEIVED: Bot #21 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:32
[2025-08-07 07:17:32] SEQUENTIAL_SKIP: Bot #21 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:32] WEBHOOK_RECEIVED: Bot #2 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:32
[2025-08-07 07:17:32] SEQUENTIAL_SKIP: Bot #2 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:32] WEBHOOK_RECEIVED: Bot #40 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:32
[2025-08-07 07:17:32] SEQUENTIAL_SKIP: Bot #40 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:32] WEBHOOK_RECEIVED: Bot #9 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:32
[2025-08-07 07:17:32] SEQUENTIAL_SKIP: Bot #9 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:32] WEBHOOK_RECEIVED: Bot #8 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:32
[2025-08-07 07:17:32] SEQUENTIAL_SKIP: Bot #8 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:32] WEBHOOK_RECEIVED: Bot #16 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:32
[2025-08-07 07:17:32] SEQUENTIAL_SKIP: Bot #16 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:32] WEBHOOK_RECEIVED: Bot #37 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:32
[2025-08-07 07:17:32] SEQUENTIAL_SKIP: Bot #37 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:17:32] SEQUENTIAL_BOT1_ERROR: Bot #1 → '🎉' (Channel: -1002888471861, Message: 269) - Bad Request: REACTION_INVALID
[2025-08-07 07:17:32] WEBHOOK_RECEIVED: Bot #13 received message from Channel: -1002888471861, Message: 269, Timestamp: 2025-08-07 07:17:32
[2025-08-07 07:17:32] SEQUENTIAL_SKIP: Bot #13 skipping immediate reaction for Channel: -1002888471861, Message: 269 (will process via queue)
[2025-08-07 07:18:06] QUEUE_SUCCESS: Bot #2 → '🏆' (Channel: -1002888471861, Message: 269, Delay: 34s)
[2025-08-07 07:18:06] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 42 remaining
[2025-08-07 07:18:06] POST_STATS: -1002888471861_269 - 1 successful, 0 errors
[2025-08-07 07:18:08] WEBHOOK_RECEIVED: Bot #24 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:08
[2025-08-07 07:18:08] SEQUENTIAL_SKIP: Bot #24 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:08] WEBHOOK_RECEIVED: Bot #5 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:08
[2025-08-07 07:18:08] SEQUENTIAL_SKIP: Bot #5 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:08] WEBHOOK_RECEIVED: Bot #23 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:08
[2025-08-07 07:18:08] SEQUENTIAL_SKIP: Bot #23 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:08] WEBHOOK_RECEIVED: Bot #3 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:08
[2025-08-07 07:18:08] SEQUENTIAL_SKIP: Bot #3 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:08] WEBHOOK_RECEIVED: Bot #2 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:08
[2025-08-07 07:18:08] SEQUENTIAL_SKIP: Bot #2 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:08] WEBHOOK_RECEIVED: Bot #25 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:08
[2025-08-07 07:18:08] SEQUENTIAL_SKIP: Bot #25 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:08] WEBHOOK_RECEIVED: Bot #41 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:08
[2025-08-07 07:18:08] SEQUENTIAL_SKIP: Bot #41 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:08] WEBHOOK_RECEIVED: Bot #20 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:08
[2025-08-07 07:18:08] SEQUENTIAL_SKIP: Bot #20 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:08] WEBHOOK_RECEIVED: Bot #21 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:08
[2025-08-07 07:18:08] SEQUENTIAL_SKIP: Bot #21 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:08] WEBHOOK_RECEIVED: Bot #37 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:08
[2025-08-07 07:18:08] SEQUENTIAL_SKIP: Bot #37 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:08] WEBHOOK_RECEIVED: Bot #10 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:08
[2025-08-07 07:18:08] SEQUENTIAL_SKIP: Bot #10 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:08] WEBHOOK_RECEIVED: Bot #30 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:08
[2025-08-07 07:18:08] SEQUENTIAL_SKIP: Bot #30 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:08] WEBHOOK_RECEIVED: Bot #1 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:08
[2025-08-07 07:18:08] SEQUENTIAL_START: Bot #1 starting sequential processing for Channel: -1002888471861, Message: 270
[2025-08-07 07:18:08] QUEUE_CREATE_START: Bot #1 creating queue for Channel: -1002888471861, Message: 270
[2025-08-07 07:18:08] QUEUE_CREATE_SUCCESS: Added 43 reactions for Channel: -1002888471861, Message: 270, Duration: 860s
[2025-08-07 07:18:09] WEBHOOK_RECEIVED: Bot #34 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:09
[2025-08-07 07:18:09] SEQUENTIAL_SKIP: Bot #34 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:09] WEBHOOK_RECEIVED: Bot #19 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:09
[2025-08-07 07:18:09] SEQUENTIAL_SKIP: Bot #19 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:09] WEBHOOK_RECEIVED: Bot #33 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:09
[2025-08-07 07:18:09] SEQUENTIAL_SKIP: Bot #33 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:09] WEBHOOK_RECEIVED: Bot #4 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:09
[2025-08-07 07:18:09] SEQUENTIAL_SKIP: Bot #4 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:09] WEBHOOK_RECEIVED: Bot #6 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:09
[2025-08-07 07:18:09] SEQUENTIAL_SKIP: Bot #6 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:09] WEBHOOK_RECEIVED: Bot #17 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:09
[2025-08-07 07:18:09] SEQUENTIAL_SKIP: Bot #17 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:09] WEBHOOK_RECEIVED: Bot #40 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:09
[2025-08-07 07:18:09] SEQUENTIAL_SKIP: Bot #40 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:09] WEBHOOK_RECEIVED: Bot #9 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:09
[2025-08-07 07:18:09] SEQUENTIAL_SKIP: Bot #9 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:09] WEBHOOK_RECEIVED: Bot #38 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:09
[2025-08-07 07:18:09] SEQUENTIAL_SKIP: Bot #38 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:09] WEBHOOK_RECEIVED: Bot #16 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:09
[2025-08-07 07:18:09] SEQUENTIAL_SKIP: Bot #16 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:09] WEBHOOK_RECEIVED: Bot #39 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:09
[2025-08-07 07:18:09] SEQUENTIAL_SKIP: Bot #39 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:09] WEBHOOK_RECEIVED: Bot #8 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:09
[2025-08-07 07:18:09] SEQUENTIAL_SKIP: Bot #8 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:09] WEBHOOK_RECEIVED: Bot #36 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:09
[2025-08-07 07:18:09] SEQUENTIAL_SKIP: Bot #36 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:09] WEBHOOK_RECEIVED: Bot #35 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:09
[2025-08-07 07:18:09] SEQUENTIAL_SKIP: Bot #35 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:09] WEBHOOK_RECEIVED: Bot #11 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:09
[2025-08-07 07:18:09] SEQUENTIAL_SKIP: Bot #11 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:09] WEBHOOK_RECEIVED: Bot #42 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:09
[2025-08-07 07:18:09] SEQUENTIAL_SKIP: Bot #42 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:09] WEBHOOK_RECEIVED: Bot #44 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:09
[2025-08-07 07:18:09] SEQUENTIAL_SKIP: Bot #44 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:09] WEBHOOK_RECEIVED: Bot #13 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:09
[2025-08-07 07:18:09] SEQUENTIAL_SKIP: Bot #13 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:09] WEBHOOK_RECEIVED: Bot #7 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:09
[2025-08-07 07:18:09] SEQUENTIAL_SKIP: Bot #7 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:09] WEBHOOK_RECEIVED: Bot #15 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:09
[2025-08-07 07:18:09] SEQUENTIAL_SKIP: Bot #15 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:09] WEBHOOK_RECEIVED: Bot #12 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:09
[2025-08-07 07:18:09] SEQUENTIAL_SKIP: Bot #12 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:09] WEBHOOK_RECEIVED: Bot #14 received message from Channel: -1002888471861, Message: 270, Timestamp: 2025-08-07 07:18:09
[2025-08-07 07:18:09] SEQUENTIAL_SKIP: Bot #14 skipping immediate reaction for Channel: -1002888471861, Message: 270 (will process via queue)
[2025-08-07 07:18:09] SEQUENTIAL_BOT1_ERROR: Bot #1 → '🎉' (Channel: -1002888471861, Message: 270) - Bad Request: REACTION_INVALID
[2025-08-07 07:19:04] QUEUE_SUCCESS: Bot #3 → '🥰' (Channel: -1002888471861, Message: 269, Delay: 92s)
[2025-08-07 07:19:05] QUEUE_SUCCESS: Bot #2 → '🥰' (Channel: -1002888471861, Message: 270, Delay: 55s)
[2025-08-07 07:19:06] QUEUE_ERROR: Bot #4 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:19:07] QUEUE_SUCCESS: Bot #3 → '💯' (Channel: -1002888471861, Message: 270, Delay: 55s)
[2025-08-07 07:19:08] QUEUE_SUCCESS: Bot #5 → '🏆' (Channel: -1002888471861, Message: 269, Delay: 92s)
[2025-08-07 07:19:08] QUEUE_SUMMARY: Processed 4 reactions, 1 errors, 80 remaining
[2025-08-07 07:19:08] POST_STATS: -1002888471861_269 - 2 successful, 1 errors
[2025-08-07 07:19:08] POST_STATS: -1002888471861_270 - 2 successful, 0 errors
[2025-08-07 07:19:33] QUEUE_SUCCESS: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:33] QUEUE_SUCCESS: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:33] QUEUE_SUCCESS: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:33] QUEUE_SUCCESS: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:33] QUEUE_SUCCESS: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:33] QUEUE_SUCCESS: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269, Delay: 121s)
[2025-08-07 07:19:34] QUEUE_SUCCESS: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269, Delay: 121s)
[2025-08-07 07:19:34] QUEUE_SUCCESS: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269, Delay: 121s)
[2025-08-07 07:19:34] QUEUE_SUCCESS: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269, Delay: 121s)
[2025-08-07 07:19:34] QUEUE_SUCCESS: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269, Delay: 121s)
[2025-08-07 07:19:34] QUEUE_SUCCESS: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:34] QUEUE_SUCCESS: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:34] QUEUE_SUCCESS: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:35] QUEUE_SUCCESS: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:35] QUEUE_SUCCESS: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:35] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:19:35] QUEUE_SUMMARY: Processed 3 reactions, 1 errors, 76 remaining
[2025-08-07 07:19:35] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:19:35] QUEUE_SUMMARY: Processed 3 reactions, 1 errors, 76 remaining
[2025-08-07 07:19:36] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:19:36] QUEUE_SUMMARY: Processed 3 reactions, 1 errors, 76 remaining
[2025-08-07 07:19:36] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:19:36] QUEUE_SUMMARY: Processed 3 reactions, 1 errors, 76 remaining
[2025-08-07 07:19:36] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:19:36] QUEUE_SUMMARY: Processed 3 reactions, 1 errors, 76 remaining
[2025-08-07 07:19:40] QUEUE_SUCCESS: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:40] QUEUE_SUCCESS: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:40] QUEUE_SUCCESS: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:40] QUEUE_ERROR: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:40] QUEUE_SUCCESS: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:40] QUEUE_ERROR: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:40] QUEUE_ERROR: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:40] QUEUE_ERROR: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:40] QUEUE_ERROR: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:40] QUEUE_ERROR: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:40] QUEUE_ERROR: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:40] QUEUE_ERROR: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:40] QUEUE_ERROR: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:40] QUEUE_SUCCESS: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:40] QUEUE_ERROR: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:40] QUEUE_ERROR: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:40] QUEUE_ERROR: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:40] QUEUE_ERROR: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:40] QUEUE_ERROR: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:40] QUEUE_ERROR: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:40] QUEUE_ERROR: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:41] QUEUE_ERROR: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:41] QUEUE_ERROR: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:41] QUEUE_ERROR: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:41] QUEUE_ERROR: Bot #4 → '🥰' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:41] QUEUE_SUCCESS: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269, Delay: 121s)
[2025-08-07 07:19:41] QUEUE_SUCCESS: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269, Delay: 121s)
[2025-08-07 07:19:41] QUEUE_SUCCESS: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269, Delay: 121s)
[2025-08-07 07:19:41] QUEUE_SUCCESS: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269, Delay: 121s)
[2025-08-07 07:19:41] QUEUE_SUCCESS: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269, Delay: 121s)
[2025-08-07 07:19:42] QUEUE_SUCCESS: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:42] QUEUE_SUCCESS: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:42] QUEUE_SUCCESS: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:42] QUEUE_SUCCESS: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:42] QUEUE_SUCCESS: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:43] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:19:43] QUEUE_SUMMARY: Processed 3 reactions, 1 errors, 76 remaining
[2025-08-07 07:19:43] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:19:43] QUEUE_SUMMARY: Processed 3 reactions, 1 errors, 76 remaining
[2025-08-07 07:19:43] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:19:43] QUEUE_SUMMARY: Processed 2 reactions, 2 errors, 76 remaining
[2025-08-07 07:19:43] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:19:43] QUEUE_SUMMARY: Processed 2 reactions, 2 errors, 76 remaining
[2025-08-07 07:19:43] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:19:43] QUEUE_SUMMARY: Processed 2 reactions, 2 errors, 76 remaining
[2025-08-07 07:19:46] QUEUE_SUCCESS: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269, Delay: 121s)
[2025-08-07 07:19:46] QUEUE_SUCCESS: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269, Delay: 122s)
[2025-08-07 07:19:46] QUEUE_SUCCESS: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269, Delay: 121s)
[2025-08-07 07:19:46] QUEUE_SUCCESS: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269, Delay: 121s)
[2025-08-07 07:19:46] QUEUE_ERROR: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:46] QUEUE_SUCCESS: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269, Delay: 121s)
[2025-08-07 07:19:46] QUEUE_ERROR: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:46] QUEUE_ERROR: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:46] QUEUE_ERROR: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:46] QUEUE_ERROR: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:46] QUEUE_ERROR: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:46] QUEUE_ERROR: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:46] QUEUE_ERROR: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:46] QUEUE_ERROR: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:46] QUEUE_ERROR: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:47] QUEUE_ERROR: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:47] QUEUE_ERROR: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:47] QUEUE_ERROR: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:47] QUEUE_ERROR: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:47] QUEUE_ERROR: Bot #6 → '🥰' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:19:47] QUEUE_SUCCESS: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:47] QUEUE_SUCCESS: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:47] QUEUE_SUCCESS: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:47] QUEUE_SUCCESS: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:47] QUEUE_SUCCESS: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 85s)
[2025-08-07 07:19:48] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:19:48] QUEUE_SUMMARY: Processed 2 reactions, 2 errors, 76 remaining
[2025-08-07 07:19:48] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:19:48] QUEUE_SUMMARY: Processed 2 reactions, 2 errors, 76 remaining
[2025-08-07 07:19:48] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:19:48] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 76 remaining
[2025-08-07 07:19:49] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:19:49] QUEUE_SUMMARY: Processed 2 reactions, 2 errors, 76 remaining
[2025-08-07 07:19:49] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:19:49] QUEUE_SUMMARY: Processed 3 reactions, 1 errors, 76 remaining
[2025-08-07 07:19:53] QUEUE_SUCCESS: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:53] QUEUE_SUCCESS: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:53] QUEUE_SUCCESS: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 84s)
[2025-08-07 07:19:53] QUEUE_SUCCESS: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 85s)
[2025-08-07 07:19:53] QUEUE_ERROR: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 15
[2025-08-07 07:19:53] QUEUE_ERROR: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 15
[2025-08-07 07:19:53] QUEUE_ERROR: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 15
[2025-08-07 07:19:53] QUEUE_ERROR: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 15
[2025-08-07 07:19:53] QUEUE_ERROR: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 15
[2025-08-07 07:19:53] QUEUE_ERROR: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 15
[2025-08-07 07:19:53] QUEUE_ERROR: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 15
[2025-08-07 07:19:53] QUEUE_ERROR: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 15
[2025-08-07 07:19:53] QUEUE_ERROR: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 15
[2025-08-07 07:19:53] QUEUE_ERROR: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 15
[2025-08-07 07:19:53] QUEUE_ERROR: Bot #5 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 15
[2025-08-07 07:19:53] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:19:53] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 76 remaining
[2025-08-07 07:19:54] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:19:54] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 76 remaining
[2025-08-07 07:19:54] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:19:54] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 76 remaining
[2025-08-07 07:19:54] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:19:54] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 76 remaining
[2025-08-07 07:19:54] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:19:54] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 76 remaining
[2025-08-07 07:19:54] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 41
[2025-08-07 07:19:54] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 41
[2025-08-07 07:19:54] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 76 remaining
[2025-08-07 07:19:54] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 76 remaining
[2025-08-07 07:19:54] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 41
[2025-08-07 07:19:54] QUEUE_SUMMARY: Processed 2 reactions, 2 errors, 76 remaining
[2025-08-07 07:19:54] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 41
[2025-08-07 07:19:54] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 41
[2025-08-07 07:19:54] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 76 remaining
[2025-08-07 07:19:54] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 76 remaining
[2025-08-07 07:19:54] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 41
[2025-08-07 07:19:54] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 76 remaining
[2025-08-07 07:19:54] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 41
[2025-08-07 07:19:54] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 76 remaining
[2025-08-07 07:19:54] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 41
[2025-08-07 07:19:54] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 76 remaining
[2025-08-07 07:19:54] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 41
[2025-08-07 07:19:54] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 76 remaining
[2025-08-07 07:19:54] QUEUE_ERROR: Bot #7 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 41
[2025-08-07 07:19:54] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 76 remaining
[2025-08-07 07:20:06] QUEUE_ERROR: Bot #6 → '🎉' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:20:07] QUEUE_SUCCESS: Bot #8 → '😘' (Channel: -1002888471861, Message: 269, Delay: 155s)
[2025-08-07 07:20:07] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 74 remaining
[2025-08-07 07:20:07] POST_STATS: -1002888471861_270 - 0 successful, 1 errors
[2025-08-07 07:20:07] POST_STATS: -1002888471861_269 - 1 successful, 0 errors
[2025-08-07 07:21:04] QUEUE_SUCCESS: Bot #7 → '🥰' (Channel: -1002888471861, Message: 270, Delay: 175s)
[2025-08-07 07:21:05] QUEUE_SUCCESS: Bot #9 → '🥰' (Channel: -1002888471861, Message: 269, Delay: 212s)
[2025-08-07 07:21:06] QUEUE_SUCCESS: Bot #8 → '🏆' (Channel: -1002888471861, Message: 270, Delay: 175s)
[2025-08-07 07:21:07] QUEUE_SUCCESS: Bot #10 → '🤩' (Channel: -1002888471861, Message: 269, Delay: 212s)
[2025-08-07 07:21:08] QUEUE_SUCCESS: Bot #9 → '💯' (Channel: -1002888471861, Message: 270, Delay: 175s)
[2025-08-07 07:21:09] QUEUE_SUCCESS: Bot #11 → '😘' (Channel: -1002888471861, Message: 269, Delay: 212s)
[2025-08-07 07:21:09] QUEUE_SUMMARY: Processed 6 reactions, 0 errors, 68 remaining
[2025-08-07 07:21:09] POST_STATS: -1002888471861_270 - 3 successful, 0 errors
[2025-08-07 07:21:09] POST_STATS: -1002888471861_269 - 3 successful, 0 errors
[2025-08-07 07:22:05] QUEUE_SUCCESS: Bot #10 → '🏆' (Channel: -1002888471861, Message: 270, Delay: 236s)
[2025-08-07 07:22:06] QUEUE_SUCCESS: Bot #12 → '🏆' (Channel: -1002888471861, Message: 269, Delay: 273s)
[2025-08-07 07:22:07] QUEUE_SUCCESS: Bot #11 → '💯' (Channel: -1002888471861, Message: 270, Delay: 236s)
[2025-08-07 07:22:08] QUEUE_SUCCESS: Bot #13 → '🤩' (Channel: -1002888471861, Message: 269, Delay: 273s)
[2025-08-07 07:22:09] QUEUE_SUCCESS: Bot #12 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 236s)
[2025-08-07 07:22:09] QUEUE_SUCCESS: Bot #14 → '🏆' (Channel: -1002888471861, Message: 269, Delay: 273s)
[2025-08-07 07:22:09] QUEUE_SUMMARY: Processed 6 reactions, 0 errors, 62 remaining
[2025-08-07 07:22:09] POST_STATS: -1002888471861_270 - 3 successful, 0 errors
[2025-08-07 07:22:09] POST_STATS: -1002888471861_269 - 3 successful, 0 errors
[2025-08-07 07:22:44] QUEUE_SUCCESS: Bot #13 → '💯' (Channel: -1002888471861, Message: 270, Delay: 275s)
[2025-08-07 07:22:44] QUEUE_SUCCESS: Bot #13 → '💯' (Channel: -1002888471861, Message: 270, Delay: 275s)
[2025-08-07 07:22:44] QUEUE_SUCCESS: Bot #13 → '💯' (Channel: -1002888471861, Message: 270, Delay: 275s)
[2025-08-07 07:22:44] QUEUE_SUCCESS: Bot #13 → '💯' (Channel: -1002888471861, Message: 270, Delay: 275s)
[2025-08-07 07:22:44] QUEUE_SUCCESS: Bot #13 → '💯' (Channel: -1002888471861, Message: 270, Delay: 275s)
[2025-08-07 07:22:45] QUEUE_SUCCESS: Bot #15 → '💯' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:22:45] QUEUE_SUCCESS: Bot #15 → '💯' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:22:45] QUEUE_SUCCESS: Bot #15 → '💯' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:22:45] QUEUE_SUCCESS: Bot #15 → '💯' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:22:45] QUEUE_SUCCESS: Bot #15 → '💯' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:22:45] QUEUE_SUCCESS: Bot #14 → '💯' (Channel: -1002888471861, Message: 270, Delay: 275s)
[2025-08-07 07:22:45] QUEUE_SUCCESS: Bot #14 → '💯' (Channel: -1002888471861, Message: 270, Delay: 275s)
[2025-08-07 07:22:46] QUEUE_SUCCESS: Bot #14 → '💯' (Channel: -1002888471861, Message: 270, Delay: 275s)
[2025-08-07 07:22:46] QUEUE_SUCCESS: Bot #14 → '💯' (Channel: -1002888471861, Message: 270, Delay: 275s)
[2025-08-07 07:22:46] QUEUE_SUCCESS: Bot #14 → '💯' (Channel: -1002888471861, Message: 270, Delay: 275s)
[2025-08-07 07:22:46] QUEUE_SUCCESS: Bot #16 → '😘' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:22:46] QUEUE_SUMMARY: Processed 4 reactions, 0 errors, 58 remaining
[2025-08-07 07:22:46] QUEUE_SUCCESS: Bot #16 → '😘' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:22:46] QUEUE_SUMMARY: Processed 4 reactions, 0 errors, 58 remaining
[2025-08-07 07:22:46] QUEUE_SUCCESS: Bot #16 → '😘' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:22:46] QUEUE_SUMMARY: Processed 4 reactions, 0 errors, 58 remaining
[2025-08-07 07:22:46] QUEUE_SUCCESS: Bot #16 → '😘' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:22:46] QUEUE_SUMMARY: Processed 4 reactions, 0 errors, 58 remaining
[2025-08-07 07:22:46] QUEUE_SUCCESS: Bot #16 → '😘' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:22:46] QUEUE_SUMMARY: Processed 4 reactions, 0 errors, 58 remaining
[2025-08-07 07:22:51] QUEUE_SUCCESS: Bot #13 → '💯' (Channel: -1002888471861, Message: 270, Delay: 275s)
[2025-08-07 07:22:51] QUEUE_SUCCESS: Bot #13 → '💯' (Channel: -1002888471861, Message: 270, Delay: 275s)
[2025-08-07 07:22:51] QUEUE_SUCCESS: Bot #13 → '💯' (Channel: -1002888471861, Message: 270, Delay: 275s)
[2025-08-07 07:22:51] QUEUE_SUCCESS: Bot #13 → '💯' (Channel: -1002888471861, Message: 270, Delay: 275s)
[2025-08-07 07:22:51] QUEUE_SUCCESS: Bot #13 → '💯' (Channel: -1002888471861, Message: 270, Delay: 275s)
[2025-08-07 07:22:52] QUEUE_SUCCESS: Bot #15 → '🏆' (Channel: -1002888471861, Message: 270, Delay: 280s)
[2025-08-07 07:22:52] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 57 remaining
[2025-08-07 07:22:52] QUEUE_SUCCESS: Bot #15 → '🏆' (Channel: -1002888471861, Message: 270, Delay: 280s)
[2025-08-07 07:22:52] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 57 remaining
[2025-08-07 07:22:52] QUEUE_SUCCESS: Bot #15 → '🏆' (Channel: -1002888471861, Message: 270, Delay: 280s)
[2025-08-07 07:22:52] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 57 remaining
[2025-08-07 07:22:52] QUEUE_SUCCESS: Bot #15 → '🏆' (Channel: -1002888471861, Message: 270, Delay: 280s)
[2025-08-07 07:22:52] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 57 remaining
[2025-08-07 07:22:52] QUEUE_SUCCESS: Bot #15 → '🏆' (Channel: -1002888471861, Message: 270, Delay: 280s)
[2025-08-07 07:22:52] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 57 remaining
[2025-08-07 07:22:54] QUEUE_ERROR: Bot #13 → '💯' (Channel: -1002888471861, Message: 270) - HTTP Unknown: No response
[2025-08-07 07:22:54] QUEUE_ERROR: Bot #13 → '💯' (Channel: -1002888471861, Message: 270) - HTTP Unknown: No response
[2025-08-07 07:22:54] QUEUE_ERROR: Bot #13 → '💯' (Channel: -1002888471861, Message: 270) - HTTP Unknown: No response
[2025-08-07 07:22:54] QUEUE_ERROR: Bot #13 → '💯' (Channel: -1002888471861, Message: 270) - HTTP Unknown: No response
[2025-08-07 07:22:54] QUEUE_ERROR: Bot #13 → '💯' (Channel: -1002888471861, Message: 270) - HTTP Unknown: No response
[2025-08-07 07:22:54] QUEUE_ERROR: Bot #13 → '💯' (Channel: -1002888471861, Message: 270) - HTTP Unknown: No response
[2025-08-07 07:22:54] QUEUE_ERROR: Bot #13 → '💯' (Channel: -1002888471861, Message: 270) - HTTP Unknown: No response
[2025-08-07 07:22:54] QUEUE_ERROR: Bot #13 → '💯' (Channel: -1002888471861, Message: 270) - HTTP Unknown: No response
[2025-08-07 07:22:54] QUEUE_ERROR: Bot #13 → '💯' (Channel: -1002888471861, Message: 270) - HTTP Unknown: No response
[2025-08-07 07:22:54] QUEUE_ERROR: Bot #13 → '💯' (Channel: -1002888471861, Message: 270) - HTTP Unknown: No response
[2025-08-07 07:22:54] QUEUE_ERROR: Bot #13 → '💯' (Channel: -1002888471861, Message: 270) - HTTP Unknown: No response
[2025-08-07 07:22:54] QUEUE_ERROR: Bot #13 → '💯' (Channel: -1002888471861, Message: 270) - HTTP Unknown: No response
[2025-08-07 07:22:54] QUEUE_ERROR: Bot #13 → '💯' (Channel: -1002888471861, Message: 270) - HTTP Unknown: No response
[2025-08-07 07:22:54] QUEUE_ERROR: Bot #13 → '💯' (Channel: -1002888471861, Message: 270) - HTTP Unknown: No response
[2025-08-07 07:22:54] QUEUE_ERROR: Bot #13 → '💯' (Channel: -1002888471861, Message: 270) - HTTP Unknown: No response
[2025-08-07 07:22:54] QUEUE_ERROR: Bot #13 → '💯' (Channel: -1002888471861, Message: 270) - HTTP Unknown: No response
[2025-08-07 07:22:54] QUEUE_ERROR: Bot #13 → '💯' (Channel: -1002888471861, Message: 270) - HTTP Unknown: No response
[2025-08-07 07:22:54] QUEUE_ERROR: Bot #13 → '💯' (Channel: -1002888471861, Message: 270) - HTTP Unknown: No response
[2025-08-07 07:22:54] QUEUE_ERROR: Bot #13 → '💯' (Channel: -1002888471861, Message: 270) - HTTP Unknown: No response
[2025-08-07 07:22:54] QUEUE_ERROR: Bot #13 → '💯' (Channel: -1002888471861, Message: 270) - HTTP Unknown: No response
[2025-08-07 07:22:57] QUEUE_SUCCESS: Bot #15 → '💯' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:22:57] QUEUE_SUCCESS: Bot #15 → '💯' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:22:57] QUEUE_SUCCESS: Bot #15 → '💯' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:22:57] QUEUE_SUCCESS: Bot #15 → '💯' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:22:57] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:22:57] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:22:57] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:22:57] QUEUE_SUCCESS: Bot #15 → '💯' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:22:57] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:22:57] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:22:57] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:22:57] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:22:57] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:22:57] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:22:57] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:22:57] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:22:57] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:22:57] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:22:57] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:22:57] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:22:57] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:22:58] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:22:58] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:22:58] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:22:58] QUEUE_ERROR: Bot #15 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:22:58] QUEUE_SUCCESS: Bot #14 → '💯' (Channel: -1002888471861, Message: 270, Delay: 275s)
[2025-08-07 07:22:58] QUEUE_SUCCESS: Bot #14 → '💯' (Channel: -1002888471861, Message: 270, Delay: 275s)
[2025-08-07 07:22:58] QUEUE_SUCCESS: Bot #14 → '💯' (Channel: -1002888471861, Message: 270, Delay: 275s)
[2025-08-07 07:22:58] QUEUE_SUCCESS: Bot #14 → '💯' (Channel: -1002888471861, Message: 270, Delay: 275s)
[2025-08-07 07:22:58] QUEUE_SUCCESS: Bot #14 → '💯' (Channel: -1002888471861, Message: 270, Delay: 276s)
[2025-08-07 07:22:59] QUEUE_SUCCESS: Bot #16 → '😘' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:22:59] QUEUE_SUMMARY: Processed 4 reactions, 0 errors, 58 remaining
[2025-08-07 07:22:59] QUEUE_SUCCESS: Bot #16 → '😘' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:22:59] QUEUE_SUMMARY: Processed 4 reactions, 0 errors, 58 remaining
[2025-08-07 07:22:59] QUEUE_SUCCESS: Bot #16 → '😘' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:22:59] QUEUE_SUMMARY: Processed 4 reactions, 0 errors, 58 remaining
[2025-08-07 07:22:59] QUEUE_SUCCESS: Bot #16 → '😘' (Channel: -1002888471861, Message: 269, Delay: 313s)
[2025-08-07 07:22:59] QUEUE_SUMMARY: Processed 2 reactions, 2 errors, 58 remaining
[2025-08-07 07:22:59] QUEUE_SUCCESS: Bot #16 → '😘' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:22:59] QUEUE_SUMMARY: Processed 4 reactions, 0 errors, 58 remaining
[2025-08-07 07:23:03] QUEUE_SUCCESS: Bot #14 → '💯' (Channel: -1002888471861, Message: 270, Delay: 275s)
[2025-08-07 07:23:03] QUEUE_SUCCESS: Bot #14 → '💯' (Channel: -1002888471861, Message: 270, Delay: 276s)
[2025-08-07 07:23:03] QUEUE_SUCCESS: Bot #14 → '💯' (Channel: -1002888471861, Message: 270, Delay: 275s)
[2025-08-07 07:23:03] QUEUE_SUCCESS: Bot #14 → '💯' (Channel: -1002888471861, Message: 270, Delay: 275s)
[2025-08-07 07:23:04] QUEUE_SUCCESS: Bot #14 → '💯' (Channel: -1002888471861, Message: 270, Delay: 275s)
[2025-08-07 07:23:04] QUEUE_ERROR: Bot #14 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:23:04] QUEUE_ERROR: Bot #14 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:23:04] QUEUE_ERROR: Bot #14 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:23:04] QUEUE_ERROR: Bot #14 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:23:04] QUEUE_ERROR: Bot #14 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:23:04] QUEUE_ERROR: Bot #14 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:23:04] QUEUE_ERROR: Bot #14 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:23:04] QUEUE_ERROR: Bot #14 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:23:04] QUEUE_ERROR: Bot #14 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:23:04] QUEUE_ERROR: Bot #14 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:23:04] QUEUE_ERROR: Bot #14 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:23:04] QUEUE_ERROR: Bot #14 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:23:04] QUEUE_ERROR: Bot #14 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:23:04] QUEUE_ERROR: Bot #14 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:23:04] QUEUE_ERROR: Bot #14 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:23:04] QUEUE_SUCCESS: Bot #16 → '😘' (Channel: -1002888471861, Message: 269, Delay: 313s)
[2025-08-07 07:23:04] QUEUE_SUMMARY: Processed 2 reactions, 2 errors, 58 remaining
[2025-08-07 07:23:04] QUEUE_SUCCESS: Bot #16 → '😘' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:23:04] QUEUE_SUMMARY: Processed 2 reactions, 2 errors, 58 remaining
[2025-08-07 07:23:04] QUEUE_SUCCESS: Bot #16 → '😘' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:23:04] QUEUE_SUMMARY: Processed 2 reactions, 2 errors, 58 remaining
[2025-08-07 07:23:04] QUEUE_SUCCESS: Bot #16 → '😘' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:23:04] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 58 remaining
[2025-08-07 07:23:04] QUEUE_SUCCESS: Bot #16 → '😘' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:23:04] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 58 remaining
[2025-08-07 07:23:05] QUEUE_SUCCESS: Bot #15 → '🏆' (Channel: -1002888471861, Message: 270, Delay: 293s)
[2025-08-07 07:23:05] QUEUE_SUCCESS: Bot #15 → '🏆' (Channel: -1002888471861, Message: 270, Delay: 293s)
[2025-08-07 07:23:05] QUEUE_SUCCESS: Bot #15 → '🏆' (Channel: -1002888471861, Message: 270, Delay: 293s)
[2025-08-07 07:23:05] QUEUE_SUCCESS: Bot #15 → '🏆' (Channel: -1002888471861, Message: 270, Delay: 293s)
[2025-08-07 07:23:05] QUEUE_SUCCESS: Bot #15 → '🏆' (Channel: -1002888471861, Message: 270, Delay: 293s)
[2025-08-07 07:23:05] QUEUE_ERROR: Bot #15 → '🏆' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 39
[2025-08-07 07:23:06] QUEUE_SUCCESS: Bot #17 → '❤️' (Channel: -1002888471861, Message: 269, Delay: 330s)
[2025-08-07 07:23:06] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 56 remaining
[2025-08-07 07:23:06] QUEUE_SUCCESS: Bot #17 → '❤️' (Channel: -1002888471861, Message: 269, Delay: 330s)
[2025-08-07 07:23:06] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 56 remaining
[2025-08-07 07:23:06] QUEUE_SUCCESS: Bot #17 → '❤️' (Channel: -1002888471861, Message: 269, Delay: 330s)
[2025-08-07 07:23:06] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 56 remaining
[2025-08-07 07:23:06] QUEUE_SUCCESS: Bot #17 → '❤️' (Channel: -1002888471861, Message: 269, Delay: 330s)
[2025-08-07 07:23:06] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 56 remaining
[2025-08-07 07:23:06] QUEUE_SUCCESS: Bot #17 → '❤️' (Channel: -1002888471861, Message: 269, Delay: 330s)
[2025-08-07 07:23:06] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 56 remaining
[2025-08-07 07:23:10] QUEUE_SUCCESS: Bot #16 → '😘' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:23:10] QUEUE_SUMMARY: Processed 2 reactions, 2 errors, 58 remaining
[2025-08-07 07:23:10] QUEUE_SUCCESS: Bot #16 → '😘' (Channel: -1002888471861, Message: 269, Delay: 313s)
[2025-08-07 07:23:10] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 58 remaining
[2025-08-07 07:23:10] QUEUE_SUCCESS: Bot #16 → '😘' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:23:10] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 58 remaining
[2025-08-07 07:23:10] QUEUE_SUCCESS: Bot #16 → '😘' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:23:10] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 58 remaining
[2025-08-07 07:23:10] QUEUE_ERROR: Bot #16 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 37
[2025-08-07 07:23:10] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 58 remaining
[2025-08-07 07:23:10] QUEUE_ERROR: Bot #16 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 37
[2025-08-07 07:23:10] QUEUE_SUCCESS: Bot #16 → '😘' (Channel: -1002888471861, Message: 269, Delay: 312s)
[2025-08-07 07:23:10] QUEUE_SUMMARY: Processed 2 reactions, 2 errors, 58 remaining
[2025-08-07 07:23:10] QUEUE_ERROR: Bot #16 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 37
[2025-08-07 07:23:10] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 58 remaining
[2025-08-07 07:23:10] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 58 remaining
[2025-08-07 07:23:10] QUEUE_ERROR: Bot #16 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 37
[2025-08-07 07:23:10] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 58 remaining
[2025-08-07 07:23:10] QUEUE_ERROR: Bot #16 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 37
[2025-08-07 07:23:10] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 58 remaining
[2025-08-07 07:23:10] QUEUE_ERROR: Bot #16 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 37
[2025-08-07 07:23:10] QUEUE_ERROR: Bot #16 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 37
[2025-08-07 07:23:10] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 58 remaining
[2025-08-07 07:23:10] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 58 remaining
[2025-08-07 07:23:10] QUEUE_ERROR: Bot #16 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 37
[2025-08-07 07:23:10] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 58 remaining
[2025-08-07 07:23:10] QUEUE_ERROR: Bot #16 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 37
[2025-08-07 07:23:10] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 58 remaining
[2025-08-07 07:23:10] QUEUE_ERROR: Bot #16 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 37
[2025-08-07 07:23:10] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 58 remaining
[2025-08-07 07:23:12] QUEUE_ERROR: Bot #15 → '🏆' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 33
[2025-08-07 07:23:12] QUEUE_ERROR: Bot #15 → '🏆' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 33
[2025-08-07 07:23:12] QUEUE_ERROR: Bot #15 → '🏆' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 33
[2025-08-07 07:23:12] QUEUE_ERROR: Bot #15 → '🏆' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 33
[2025-08-07 07:23:12] QUEUE_ERROR: Bot #15 → '🏆' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 32
[2025-08-07 07:23:12] QUEUE_ERROR: Bot #15 → '🏆' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 32
[2025-08-07 07:23:12] QUEUE_ERROR: Bot #15 → '🏆' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 32
[2025-08-07 07:23:12] QUEUE_ERROR: Bot #15 → '🏆' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 32
[2025-08-07 07:23:12] QUEUE_ERROR: Bot #15 → '🏆' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 32
[2025-08-07 07:23:12] QUEUE_ERROR: Bot #15 → '🏆' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 32
[2025-08-07 07:23:12] QUEUE_ERROR: Bot #15 → '🏆' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 32
[2025-08-07 07:23:12] QUEUE_ERROR: Bot #15 → '🏆' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 32
[2025-08-07 07:23:12] QUEUE_ERROR: Bot #15 → '🏆' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 32
[2025-08-07 07:23:12] QUEUE_ERROR: Bot #15 → '🏆' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 32
[2025-08-07 07:23:12] QUEUE_ERROR: Bot #15 → '🏆' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 32
[2025-08-07 07:23:13] QUEUE_SUCCESS: Bot #17 → '❤️' (Channel: -1002888471861, Message: 269, Delay: 333s)
[2025-08-07 07:23:13] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 56 remaining
[2025-08-07 07:23:13] POST_STATS: -1002888471861_270 - 0 successful, 1 errors
[2025-08-07 07:23:13] POST_STATS: -1002888471861_269 - 1 successful, 0 errors
[2025-08-07 07:23:13] QUEUE_SUCCESS: Bot #17 → '❤️' (Channel: -1002888471861, Message: 269, Delay: 340s)
[2025-08-07 07:23:13] QUEUE_SUCCESS: Bot #17 → '❤️' (Channel: -1002888471861, Message: 269, Delay: 340s)
[2025-08-07 07:23:13] QUEUE_SUCCESS: Bot #17 → '❤️' (Channel: -1002888471861, Message: 269, Delay: 340s)
[2025-08-07 07:23:13] QUEUE_SUCCESS: Bot #17 → '❤️' (Channel: -1002888471861, Message: 269, Delay: 340s)
[2025-08-07 07:23:14] QUEUE_ERROR: Bot #16 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 32
[2025-08-07 07:23:14] QUEUE_ERROR: Bot #16 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 32
[2025-08-07 07:23:14] QUEUE_ERROR: Bot #16 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 32
[2025-08-07 07:23:14] QUEUE_ERROR: Bot #16 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 32
[2025-08-07 07:23:15] QUEUE_ERROR: Bot #18 → '🤩' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:23:15] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 54 remaining
[2025-08-07 07:23:15] QUEUE_ERROR: Bot #18 → '🤩' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:23:15] QUEUE_ERROR: Bot #18 → '🤩' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:23:15] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 54 remaining
[2025-08-07 07:23:15] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 54 remaining
[2025-08-07 07:23:15] QUEUE_ERROR: Bot #18 → '🤩' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:23:15] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 54 remaining
[2025-08-07 07:23:18] QUEUE_SUCCESS: Bot #17 → '❤️' (Channel: -1002888471861, Message: 269, Delay: 340s)
[2025-08-07 07:23:18] QUEUE_SUCCESS: Bot #17 → '❤️' (Channel: -1002888471861, Message: 269, Delay: 340s)
[2025-08-07 07:23:18] QUEUE_SUCCESS: Bot #17 → '❤️' (Channel: -1002888471861, Message: 269, Delay: 340s)
[2025-08-07 07:23:18] QUEUE_SUCCESS: Bot #17 → '❤️' (Channel: -1002888471861, Message: 269, Delay: 340s)
[2025-08-07 07:23:18] QUEUE_ERROR: Bot #17 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:23:18] QUEUE_SUCCESS: Bot #17 → '❤️' (Channel: -1002888471861, Message: 269, Delay: 340s)
[2025-08-07 07:23:18] QUEUE_ERROR: Bot #17 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:23:18] QUEUE_ERROR: Bot #17 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:23:18] QUEUE_ERROR: Bot #17 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:23:18] QUEUE_ERROR: Bot #17 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:23:18] QUEUE_ERROR: Bot #17 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:23:19] QUEUE_ERROR: Bot #16 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 27
[2025-08-07 07:23:19] QUEUE_ERROR: Bot #16 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 27
[2025-08-07 07:23:19] QUEUE_ERROR: Bot #16 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 27
[2025-08-07 07:23:19] QUEUE_ERROR: Bot #16 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 27
[2025-08-07 07:23:19] QUEUE_ERROR: Bot #16 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 27
[2025-08-07 07:23:19] QUEUE_ERROR: Bot #16 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 27
[2025-08-07 07:23:19] QUEUE_ERROR: Bot #16 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 27
[2025-08-07 07:23:19] QUEUE_ERROR: Bot #16 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 27
[2025-08-07 07:23:19] QUEUE_ERROR: Bot #16 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 27
[2025-08-07 07:23:19] QUEUE_ERROR: Bot #16 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 27
[2025-08-07 07:23:19] QUEUE_ERROR: Bot #16 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 27
[2025-08-07 07:23:20] QUEUE_ERROR: Bot #18 → '🤩' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:23:20] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 54 remaining
[2025-08-07 07:23:20] QUEUE_ERROR: Bot #18 → '🤩' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:23:20] QUEUE_ERROR: Bot #18 → '🤩' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:23:20] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 54 remaining
[2025-08-07 07:23:20] QUEUE_ERROR: Bot #18 → '🤩' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:23:20] QUEUE_ERROR: Bot #18 → '🤩' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:23:20] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 54 remaining
[2025-08-07 07:23:20] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 54 remaining
[2025-08-07 07:23:20] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 54 remaining
[2025-08-07 07:23:20] QUEUE_ERROR: Bot #18 → '🤩' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:23:20] QUEUE_ERROR: Bot #18 → '🤩' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:23:20] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 54 remaining
[2025-08-07 07:23:20] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 54 remaining
[2025-08-07 07:23:20] QUEUE_ERROR: Bot #18 → '🤩' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:23:20] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 54 remaining
[2025-08-07 07:23:20] QUEUE_ERROR: Bot #18 → '🤩' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:23:20] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 54 remaining
[2025-08-07 07:23:20] QUEUE_ERROR: Bot #18 → '🤩' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:23:20] QUEUE_ERROR: Bot #18 → '🤩' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:23:20] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 54 remaining
[2025-08-07 07:23:20] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 54 remaining
[2025-08-07 07:23:47] QUEUE_SUCCESS: Bot #17 → '🏆' (Channel: -1002888471861, Message: 270, Delay: 338s)
[2025-08-07 07:23:47] QUEUE_SUCCESS: Bot #17 → '🏆' (Channel: -1002888471861, Message: 270, Delay: 338s)
[2025-08-07 07:23:47] QUEUE_SUCCESS: Bot #17 → '🏆' (Channel: -1002888471861, Message: 270, Delay: 338s)
[2025-08-07 07:23:47] QUEUE_SUCCESS: Bot #17 → '🏆' (Channel: -1002888471861, Message: 270, Delay: 338s)
[2025-08-07 07:23:47] QUEUE_SUCCESS: Bot #17 → '🏆' (Channel: -1002888471861, Message: 270, Delay: 338s)
[2025-08-07 07:23:47] QUEUE_ERROR: Bot #17 → '🏆' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 18
[2025-08-07 07:23:47] QUEUE_ERROR: Bot #17 → '🏆' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 18
[2025-08-07 07:23:48] QUEUE_SUCCESS: Bot #19 → '🤩' (Channel: -1002888471861, Message: 269, Delay: 375s)
[2025-08-07 07:23:48] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 52 remaining
[2025-08-07 07:23:48] QUEUE_SUCCESS: Bot #19 → '🤩' (Channel: -1002888471861, Message: 269, Delay: 375s)
[2025-08-07 07:23:48] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 52 remaining
[2025-08-07 07:23:48] QUEUE_SUCCESS: Bot #19 → '🤩' (Channel: -1002888471861, Message: 269, Delay: 375s)
[2025-08-07 07:23:48] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 52 remaining
[2025-08-07 07:23:48] QUEUE_SUCCESS: Bot #19 → '🤩' (Channel: -1002888471861, Message: 269, Delay: 375s)
[2025-08-07 07:23:48] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 52 remaining
[2025-08-07 07:23:48] QUEUE_SUCCESS: Bot #19 → '🤩' (Channel: -1002888471861, Message: 269, Delay: 375s)
[2025-08-07 07:23:48] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 52 remaining
[2025-08-07 07:23:50] QUEUE_ERROR: Bot #18 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:23:50] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 51 remaining
[2025-08-07 07:23:50] QUEUE_ERROR: Bot #18 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:23:50] QUEUE_ERROR: Bot #18 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:23:50] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 51 remaining
[2025-08-07 07:23:50] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 51 remaining
[2025-08-07 07:23:50] QUEUE_ERROR: Bot #18 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:23:50] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 51 remaining
[2025-08-07 07:23:50] QUEUE_ERROR: Bot #18 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:23:50] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 51 remaining
[2025-08-07 07:23:53] QUEUE_SUCCESS: Bot #19 → '🤩' (Channel: -1002888471861, Message: 269, Delay: 375s)
[2025-08-07 07:23:53] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 52 remaining
[2025-08-07 07:23:53] QUEUE_SUCCESS: Bot #19 → '🤩' (Channel: -1002888471861, Message: 269, Delay: 375s)
[2025-08-07 07:23:53] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 52 remaining
[2025-08-07 07:23:56] QUEUE_ERROR: Bot #18 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:23:56] QUEUE_ERROR: Bot #18 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:23:57] QUEUE_SUCCESS: Bot #20 → '🤩' (Channel: -1002888471861, Message: 269, Delay: 384s)
[2025-08-07 07:23:57] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 50 remaining
[2025-08-07 07:23:57] QUEUE_SUCCESS: Bot #20 → '🤩' (Channel: -1002888471861, Message: 269, Delay: 384s)
[2025-08-07 07:23:57] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 50 remaining
[2025-08-07 07:24:17] QUEUE_SUCCESS: Bot #19 → '🏆' (Channel: -1002888471861, Message: 270, Delay: 368s)
[2025-08-07 07:24:17] QUEUE_SUCCESS: Bot #19 → '🏆' (Channel: -1002888471861, Message: 270, Delay: 368s)
[2025-08-07 07:24:17] QUEUE_SUCCESS: Bot #19 → '🏆' (Channel: -1002888471861, Message: 270, Delay: 368s)
[2025-08-07 07:24:17] QUEUE_SUCCESS: Bot #19 → '🏆' (Channel: -1002888471861, Message: 270, Delay: 368s)
[2025-08-07 07:24:17] QUEUE_SUCCESS: Bot #19 → '🏆' (Channel: -1002888471861, Message: 270, Delay: 368s)
[2025-08-07 07:24:18] QUEUE_SUCCESS: Bot #21 → '😘' (Channel: -1002888471861, Message: 269, Delay: 405s)
[2025-08-07 07:24:18] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 48 remaining
[2025-08-07 07:24:18] QUEUE_SUCCESS: Bot #21 → '😘' (Channel: -1002888471861, Message: 269, Delay: 405s)
[2025-08-07 07:24:18] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 48 remaining
[2025-08-07 07:24:18] QUEUE_SUCCESS: Bot #21 → '😘' (Channel: -1002888471861, Message: 269, Delay: 405s)
[2025-08-07 07:24:18] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 48 remaining
[2025-08-07 07:24:18] QUEUE_SUCCESS: Bot #21 → '😘' (Channel: -1002888471861, Message: 269, Delay: 405s)
[2025-08-07 07:24:18] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 48 remaining
[2025-08-07 07:24:18] QUEUE_SUCCESS: Bot #21 → '😘' (Channel: -1002888471861, Message: 269, Delay: 405s)
[2025-08-07 07:24:18] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 48 remaining
[2025-08-07 07:24:22] QUEUE_SUCCESS: Bot #19 → '🏆' (Channel: -1002888471861, Message: 270, Delay: 368s)
[2025-08-07 07:24:22] QUEUE_SUCCESS: Bot #19 → '🏆' (Channel: -1002888471861, Message: 270, Delay: 368s)
[2025-08-07 07:24:23] QUEUE_SUCCESS: Bot #21 → '😘' (Channel: -1002888471861, Message: 269, Delay: 405s)
[2025-08-07 07:24:23] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 48 remaining
[2025-08-07 07:24:23] QUEUE_SUCCESS: Bot #21 → '😘' (Channel: -1002888471861, Message: 269, Delay: 405s)
[2025-08-07 07:24:23] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 48 remaining
[2025-08-07 07:24:56] QUEUE_SUCCESS: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 407s)
[2025-08-07 07:24:56] QUEUE_SUCCESS: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 407s)
[2025-08-07 07:24:56] QUEUE_SUCCESS: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 407s)
[2025-08-07 07:24:56] QUEUE_SUCCESS: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 407s)
[2025-08-07 07:24:56] QUEUE_SUCCESS: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 407s)
[2025-08-07 07:24:57] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:24:57] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:24:57] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:24:57] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:24:57] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:24:58] QUEUE_SUCCESS: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 407s)
[2025-08-07 07:24:58] QUEUE_SUCCESS: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 407s)
[2025-08-07 07:24:58] QUEUE_SUCCESS: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 407s)
[2025-08-07 07:24:58] QUEUE_SUCCESS: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 407s)
[2025-08-07 07:24:58] QUEUE_SUCCESS: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 407s)
[2025-08-07 07:24:59] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:24:59] QUEUE_SUMMARY: Processed 2 reactions, 2 errors, 44 remaining
[2025-08-07 07:24:59] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:24:59] QUEUE_SUMMARY: Processed 2 reactions, 2 errors, 44 remaining
[2025-08-07 07:24:59] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:24:59] QUEUE_SUMMARY: Processed 2 reactions, 2 errors, 44 remaining
[2025-08-07 07:25:00] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:25:00] QUEUE_SUMMARY: Processed 2 reactions, 2 errors, 44 remaining
[2025-08-07 07:25:00] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:25:00] QUEUE_SUMMARY: Processed 2 reactions, 2 errors, 44 remaining
[2025-08-07 07:25:02] QUEUE_SUCCESS: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 407s)
[2025-08-07 07:25:02] QUEUE_SUCCESS: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 407s)
[2025-08-07 07:25:02] QUEUE_SUCCESS: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 407s)
[2025-08-07 07:25:02] QUEUE_SUCCESS: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 407s)
[2025-08-07 07:25:02] QUEUE_SUCCESS: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 408s)
[2025-08-07 07:25:02] QUEUE_ERROR: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:25:02] QUEUE_ERROR: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:25:02] QUEUE_ERROR: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:25:02] QUEUE_ERROR: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:25:02] QUEUE_ERROR: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:25:02] QUEUE_ERROR: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:25:02] QUEUE_ERROR: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:25:02] QUEUE_ERROR: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:25:02] QUEUE_ERROR: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:25:02] QUEUE_ERROR: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:25:02] QUEUE_ERROR: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:25:02] QUEUE_ERROR: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:25:02] QUEUE_ERROR: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:25:02] QUEUE_ERROR: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:25:02] QUEUE_ERROR: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:25:02] QUEUE_ERROR: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:25:02] QUEUE_ERROR: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:25:02] QUEUE_ERROR: Bot #20 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:25:03] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:25:03] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:25:03] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:25:03] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:25:03] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:25:03] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:25:03] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:25:03] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:25:03] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:25:03] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:25:03] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:25:03] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:25:03] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:25:03] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:25:03] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:25:03] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:25:03] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:25:03] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:25:03] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:25:03] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:25:03] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:25:03] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:25:03] QUEUE_ERROR: Bot #22 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:25:03] QUEUE_SUCCESS: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 407s)
[2025-08-07 07:25:03] QUEUE_SUCCESS: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 408s)
[2025-08-07 07:25:04] QUEUE_SUCCESS: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 407s)
[2025-08-07 07:25:04] QUEUE_SUCCESS: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 407s)
[2025-08-07 07:25:04] QUEUE_SUCCESS: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 407s)
[2025-08-07 07:25:04] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:25:04] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 44 remaining
[2025-08-07 07:25:05] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:25:05] QUEUE_SUMMARY: Processed 2 reactions, 2 errors, 44 remaining
[2025-08-07 07:25:05] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:25:05] QUEUE_SUMMARY: Processed 2 reactions, 2 errors, 44 remaining
[2025-08-07 07:25:05] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:25:05] QUEUE_SUMMARY: Processed 2 reactions, 2 errors, 44 remaining
[2025-08-07 07:25:05] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:25:05] QUEUE_SUMMARY: Processed 2 reactions, 2 errors, 44 remaining
[2025-08-07 07:25:09] QUEUE_SUCCESS: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 408s)
[2025-08-07 07:25:09] QUEUE_SUCCESS: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 408s)
[2025-08-07 07:25:09] QUEUE_SUCCESS: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 408s)
[2025-08-07 07:25:09] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 8
[2025-08-07 07:25:09] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 8
[2025-08-07 07:25:09] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 8
[2025-08-07 07:25:09] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 8
[2025-08-07 07:25:09] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 8
[2025-08-07 07:25:09] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 8
[2025-08-07 07:25:09] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 8
[2025-08-07 07:25:09] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 8
[2025-08-07 07:25:09] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 8
[2025-08-07 07:25:09] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 8
[2025-08-07 07:25:09] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 8
[2025-08-07 07:25:09] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 8
[2025-08-07 07:25:09] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 8
[2025-08-07 07:25:09] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 8
[2025-08-07 07:25:09] QUEUE_ERROR: Bot #21 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 8
[2025-08-07 07:25:10] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:25:10] QUEUE_SUMMARY: Processed 2 reactions, 2 errors, 44 remaining
[2025-08-07 07:25:10] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:25:10] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 44 remaining
[2025-08-07 07:25:10] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:25:10] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 44 remaining
[2025-08-07 07:25:10] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:25:10] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 44 remaining
[2025-08-07 07:25:10] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:25:10] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 44 remaining
[2025-08-07 07:25:15] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:25:15] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 44 remaining
[2025-08-07 07:25:15] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:25:15] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 44 remaining
[2025-08-07 07:25:15] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:25:15] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 44 remaining
[2025-08-07 07:25:15] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:25:15] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 44 remaining
[2025-08-07 07:25:16] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:25:16] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 44 remaining
[2025-08-07 07:25:16] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 43
[2025-08-07 07:25:16] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 43
[2025-08-07 07:25:16] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 44 remaining
[2025-08-07 07:25:16] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 44 remaining
[2025-08-07 07:25:16] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 43
[2025-08-07 07:25:16] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 44 remaining
[2025-08-07 07:25:16] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 43
[2025-08-07 07:25:16] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 43
[2025-08-07 07:25:16] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 44 remaining
[2025-08-07 07:25:16] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 44 remaining
[2025-08-07 07:25:16] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 43
[2025-08-07 07:25:16] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 44 remaining
[2025-08-07 07:25:16] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 43
[2025-08-07 07:25:16] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 44 remaining
[2025-08-07 07:25:16] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 43
[2025-08-07 07:25:16] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 44 remaining
[2025-08-07 07:26:05] QUEUE_ERROR: Bot #22 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:26:05] QUEUE_SUCCESS: Bot #24 → '🤩' (Channel: -1002888471861, Message: 269, Delay: 513s)
[2025-08-07 07:26:06] QUEUE_ERROR: Bot #23 → '🎉' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:26:07] QUEUE_SUCCESS: Bot #25 → '❤️' (Channel: -1002888471861, Message: 269, Delay: 513s)
[2025-08-07 07:26:08] QUEUE_SUCCESS: Bot #24 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 476s)
[2025-08-07 07:26:09] QUEUE_ERROR: Bot #26 → '🏆' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:26:09] QUEUE_SUMMARY: Processed 3 reactions, 3 errors, 38 remaining
[2025-08-07 07:26:09] POST_STATS: -1002888471861_270 - 1 successful, 2 errors
[2025-08-07 07:26:09] POST_STATS: -1002888471861_269 - 2 successful, 1 errors
[2025-08-07 07:27:05] QUEUE_ERROR: Bot #25 → '🎉' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:27:06] QUEUE_ERROR: Bot #27 → '🏆' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:06] QUEUE_ERROR: Bot #26 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:09] QUEUE_ERROR: Bot #28 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:27:10] QUEUE_ERROR: Bot #27 → '🎉' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:11] QUEUE_ERROR: Bot #29 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:11] QUEUE_SUMMARY: Processed 0 reactions, 6 errors, 32 remaining
[2025-08-07 07:27:11] POST_STATS: -1002888471861_270 - 0 successful, 3 errors
[2025-08-07 07:27:11] POST_STATS: -1002888471861_269 - 0 successful, 3 errors
[2025-08-07 07:27:40] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:27:40] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:27:40] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:27:40] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:27:40] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:27:40] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:27:40] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:27:40] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:27:40] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:27:40] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:27:40] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:27:40] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:27:40] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:27:40] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:27:40] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:27:40] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:27:40] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:27:40] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:27:40] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:27:40] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:27:40] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:27:40] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:27:40] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:27:40] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:27:40] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:27:41] QUEUE_SUCCESS: Bot #30 → '😘' (Channel: -1002888471861, Message: 269, Delay: 608s)
[2025-08-07 07:27:41] QUEUE_SUCCESS: Bot #30 → '😘' (Channel: -1002888471861, Message: 269, Delay: 608s)
[2025-08-07 07:27:41] QUEUE_SUCCESS: Bot #30 → '😘' (Channel: -1002888471861, Message: 269, Delay: 608s)
[2025-08-07 07:27:41] QUEUE_SUCCESS: Bot #30 → '😘' (Channel: -1002888471861, Message: 269, Delay: 608s)
[2025-08-07 07:27:41] QUEUE_SUCCESS: Bot #30 → '😘' (Channel: -1002888471861, Message: 269, Delay: 608s)
[2025-08-07 07:27:41] QUEUE_ERROR: Bot #28 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:27:42] QUEUE_ERROR: Bot #29 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:42] QUEUE_ERROR: Bot #29 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:42] QUEUE_ERROR: Bot #29 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:42] QUEUE_ERROR: Bot #29 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:42] QUEUE_ERROR: Bot #29 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:42] QUEUE_ERROR: Bot #31 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:42] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 28 remaining
[2025-08-07 07:27:42] QUEUE_ERROR: Bot #31 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:42] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 28 remaining
[2025-08-07 07:27:42] QUEUE_ERROR: Bot #31 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:42] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 28 remaining
[2025-08-07 07:27:42] QUEUE_ERROR: Bot #31 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:42] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 28 remaining
[2025-08-07 07:27:42] QUEUE_ERROR: Bot #31 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:42] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 28 remaining
[2025-08-07 07:27:48] QUEUE_SUCCESS: Bot #30 → '😘' (Channel: -1002888471861, Message: 269, Delay: 608s)
[2025-08-07 07:27:48] QUEUE_SUCCESS: Bot #30 → '😘' (Channel: -1002888471861, Message: 269, Delay: 608s)
[2025-08-07 07:27:48] QUEUE_SUCCESS: Bot #30 → '😘' (Channel: -1002888471861, Message: 269, Delay: 608s)
[2025-08-07 07:27:48] QUEUE_SUCCESS: Bot #30 → '😘' (Channel: -1002888471861, Message: 269, Delay: 608s)
[2025-08-07 07:27:48] QUEUE_SUCCESS: Bot #30 → '😘' (Channel: -1002888471861, Message: 269, Delay: 608s)
[2025-08-07 07:27:48] QUEUE_ERROR: Bot #30 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:27:48] QUEUE_ERROR: Bot #30 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:27:48] QUEUE_ERROR: Bot #30 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:27:48] QUEUE_ERROR: Bot #30 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:27:48] QUEUE_ERROR: Bot #30 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:27:48] QUEUE_ERROR: Bot #30 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:27:48] QUEUE_ERROR: Bot #30 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:27:48] QUEUE_ERROR: Bot #30 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:27:48] QUEUE_ERROR: Bot #30 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:27:48] QUEUE_ERROR: Bot #30 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:27:48] QUEUE_ERROR: Bot #30 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:27:49] QUEUE_ERROR: Bot #30 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:27:49] QUEUE_ERROR: Bot #30 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:27:49] QUEUE_ERROR: Bot #30 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:27:49] QUEUE_ERROR: Bot #30 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:27:49] QUEUE_ERROR: Bot #30 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:27:49] QUEUE_ERROR: Bot #29 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:49] QUEUE_ERROR: Bot #29 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:49] QUEUE_ERROR: Bot #29 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:49] QUEUE_ERROR: Bot #29 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:49] QUEUE_ERROR: Bot #29 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:49] QUEUE_ERROR: Bot #29 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:49] QUEUE_ERROR: Bot #29 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:49] QUEUE_ERROR: Bot #29 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:49] QUEUE_ERROR: Bot #29 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:49] QUEUE_ERROR: Bot #29 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:49] QUEUE_ERROR: Bot #29 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:49] QUEUE_ERROR: Bot #29 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:49] QUEUE_ERROR: Bot #29 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:49] QUEUE_ERROR: Bot #29 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:49] QUEUE_ERROR: Bot #29 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:49] QUEUE_ERROR: Bot #29 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:49] QUEUE_ERROR: Bot #29 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:49] QUEUE_ERROR: Bot #29 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:49] QUEUE_ERROR: Bot #29 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:49] QUEUE_ERROR: Bot #29 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:49] QUEUE_ERROR: Bot #29 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:50] QUEUE_ERROR: Bot #31 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:50] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 28 remaining
[2025-08-07 07:27:50] QUEUE_ERROR: Bot #31 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:50] QUEUE_ERROR: Bot #31 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:50] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 28 remaining
[2025-08-07 07:27:50] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 28 remaining
[2025-08-07 07:27:50] QUEUE_ERROR: Bot #31 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:50] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 28 remaining
[2025-08-07 07:27:50] QUEUE_ERROR: Bot #31 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:50] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 28 remaining
[2025-08-07 07:27:50] QUEUE_ERROR: Bot #31 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:50] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 28 remaining
[2025-08-07 07:27:50] QUEUE_ERROR: Bot #31 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:50] QUEUE_ERROR: Bot #31 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:50] QUEUE_ERROR: Bot #31 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:50] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 28 remaining
[2025-08-07 07:27:50] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 28 remaining
[2025-08-07 07:27:50] QUEUE_ERROR: Bot #31 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:50] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 28 remaining
[2025-08-07 07:27:50] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 28 remaining
[2025-08-07 07:27:50] QUEUE_ERROR: Bot #31 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:50] QUEUE_ERROR: Bot #31 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:50] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 28 remaining
[2025-08-07 07:27:50] QUEUE_ERROR: Bot #31 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:50] QUEUE_ERROR: Bot #31 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:50] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 28 remaining
[2025-08-07 07:27:50] QUEUE_ERROR: Bot #31 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:50] QUEUE_ERROR: Bot #31 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:50] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 28 remaining
[2025-08-07 07:27:50] QUEUE_ERROR: Bot #31 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:50] QUEUE_ERROR: Bot #31 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:50] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 28 remaining
[2025-08-07 07:27:50] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 28 remaining
[2025-08-07 07:27:50] QUEUE_ERROR: Bot #31 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:50] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 28 remaining
[2025-08-07 07:27:50] QUEUE_ERROR: Bot #31 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:50] QUEUE_ERROR: Bot #31 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:50] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 28 remaining
[2025-08-07 07:27:50] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 28 remaining
[2025-08-07 07:27:50] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 28 remaining
[2025-08-07 07:27:50] QUEUE_SUMMARY: Processed 0 reactions, 4 errors, 28 remaining
[2025-08-07 07:27:50] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 28 remaining
[2025-08-07 07:27:56] QUEUE_SUCCESS: Bot #30 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 583s)
[2025-08-07 07:27:56] QUEUE_SUCCESS: Bot #30 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 583s)
[2025-08-07 07:27:56] QUEUE_SUCCESS: Bot #30 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 584s)
[2025-08-07 07:27:56] QUEUE_SUCCESS: Bot #30 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 584s)
[2025-08-07 07:27:56] QUEUE_SUCCESS: Bot #30 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 584s)
[2025-08-07 07:27:56] QUEUE_ERROR: Bot #32 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:56] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 26 remaining
[2025-08-07 07:27:57] QUEUE_ERROR: Bot #32 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:57] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 26 remaining
[2025-08-07 07:27:57] QUEUE_ERROR: Bot #32 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:57] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 26 remaining
[2025-08-07 07:27:57] QUEUE_ERROR: Bot #32 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:57] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 26 remaining
[2025-08-07 07:27:57] QUEUE_ERROR: Bot #32 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:27:57] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 26 remaining
[2025-08-07 07:28:00] QUEUE_ERROR: Bot #30 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:28:00] QUEUE_ERROR: Bot #30 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:28:00] QUEUE_ERROR: Bot #30 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:28:00] QUEUE_ERROR: Bot #30 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:28:00] QUEUE_ERROR: Bot #30 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:28:00] QUEUE_ERROR: Bot #30 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:28:00] QUEUE_ERROR: Bot #30 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:28:00] QUEUE_ERROR: Bot #30 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:28:00] QUEUE_ERROR: Bot #30 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:28:00] QUEUE_ERROR: Bot #30 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:28:00] QUEUE_ERROR: Bot #30 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:28:00] QUEUE_ERROR: Bot #30 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:28:00] QUEUE_ERROR: Bot #30 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:28:00] QUEUE_ERROR: Bot #30 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:28:00] QUEUE_ERROR: Bot #30 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:28:00] QUEUE_ERROR: Bot #30 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:28:00] QUEUE_ERROR: Bot #30 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:28:01] QUEUE_ERROR: Bot #32 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:28:01] QUEUE_ERROR: Bot #32 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:28:01] QUEUE_ERROR: Bot #32 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:28:01] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 26 remaining
[2025-08-07 07:28:01] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 26 remaining
[2025-08-07 07:28:01] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 26 remaining
[2025-08-07 07:28:01] QUEUE_ERROR: Bot #32 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:28:01] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 26 remaining
[2025-08-07 07:28:01] QUEUE_ERROR: Bot #32 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:28:01] QUEUE_ERROR: Bot #32 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:28:01] QUEUE_ERROR: Bot #32 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:28:01] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 26 remaining
[2025-08-07 07:28:01] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 26 remaining
[2025-08-07 07:28:01] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 26 remaining
[2025-08-07 07:28:01] QUEUE_ERROR: Bot #32 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:28:01] QUEUE_ERROR: Bot #32 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:28:01] QUEUE_ERROR: Bot #32 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:28:01] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 26 remaining
[2025-08-07 07:28:01] QUEUE_ERROR: Bot #32 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:28:01] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 26 remaining
[2025-08-07 07:28:01] QUEUE_ERROR: Bot #32 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:28:01] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 26 remaining
[2025-08-07 07:28:01] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 26 remaining
[2025-08-07 07:28:01] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 26 remaining
[2025-08-07 07:28:01] QUEUE_ERROR: Bot #32 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:28:01] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 26 remaining
[2025-08-07 07:28:01] QUEUE_ERROR: Bot #32 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:28:01] QUEUE_ERROR: Bot #32 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:28:01] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 26 remaining
[2025-08-07 07:28:01] QUEUE_ERROR: Bot #32 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:28:01] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 26 remaining
[2025-08-07 07:28:01] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 26 remaining
[2025-08-07 07:28:01] QUEUE_ERROR: Bot #32 → '❤️' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:28:01] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 26 remaining
[2025-08-07 07:28:15] QUEUE_ERROR: Bot #31 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:28:16] QUEUE_SUCCESS: Bot #33 → '🏆' (Channel: -1002888471861, Message: 269, Delay: 643s)
[2025-08-07 07:28:16] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 24 remaining
[2025-08-07 07:29:05] QUEUE_ERROR: Bot #32 → '🎉' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:29:06] QUEUE_ERROR: Bot #34 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:29:06] QUEUE_ERROR: Bot #33 → '🎉' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:29:07] QUEUE_SUCCESS: Bot #35 → '❤️' (Channel: -1002888471861, Message: 269, Delay: 693s)
[2025-08-07 07:29:07] QUEUE_SUMMARY: Processed 1 reactions, 3 errors, 20 remaining
[2025-08-07 07:29:07] POST_STATS: -1002888471861_270 - 0 successful, 2 errors
[2025-08-07 07:29:07] POST_STATS: -1002888471861_269 - 1 successful, 1 errors
[2025-08-07 07:30:06] QUEUE_SUCCESS: Bot #34 → '💯' (Channel: -1002888471861, Message: 270, Delay: 717s)
[2025-08-07 07:30:07] QUEUE_ERROR: Bot #36 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:30:07] QUEUE_SUCCESS: Bot #35 → '💯' (Channel: -1002888471861, Message: 270, Delay: 717s)
[2025-08-07 07:30:08] QUEUE_SUCCESS: Bot #37 → '❤️' (Channel: -1002888471861, Message: 269, Delay: 754s)
[2025-08-07 07:30:09] QUEUE_ERROR: Bot #36 → '🎉' (Channel: -1002888471861, Message: 270) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:30:10] QUEUE_SUCCESS: Bot #38 → '🥰' (Channel: -1002888471861, Message: 269, Delay: 754s)
[2025-08-07 07:30:10] QUEUE_SUMMARY: Processed 4 reactions, 2 errors, 14 remaining
[2025-08-07 07:30:10] POST_STATS: -1002888471861_270 - 2 successful, 1 errors
[2025-08-07 07:30:10] POST_STATS: -1002888471861_269 - 2 successful, 1 errors
[2025-08-07 07:31:05] QUEUE_SUCCESS: Bot #37 → '🏆' (Channel: -1002888471861, Message: 270, Delay: 776s)
[2025-08-07 07:31:05] QUEUE_SUCCESS: Bot #39 → '🥰' (Channel: -1002888471861, Message: 269, Delay: 813s)
[2025-08-07 07:31:06] QUEUE_SUCCESS: Bot #38 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 776s)
[2025-08-07 07:31:07] QUEUE_SUCCESS: Bot #40 → '🏆' (Channel: -1002888471861, Message: 269, Delay: 813s)
[2025-08-07 07:31:08] QUEUE_SUCCESS: Bot #39 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 776s)
[2025-08-07 07:31:09] QUEUE_SUCCESS: Bot #41 → '🤩' (Channel: -1002888471861, Message: 269, Delay: 813s)
[2025-08-07 07:31:09] QUEUE_SUMMARY: Processed 6 reactions, 0 errors, 8 remaining
[2025-08-07 07:31:09] POST_STATS: -1002888471861_270 - 3 successful, 0 errors
[2025-08-07 07:31:09] POST_STATS: -1002888471861_269 - 3 successful, 0 errors
[2025-08-07 07:31:52] QUEUE_SUCCESS: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:31:52] QUEUE_SUCCESS: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:31:52] QUEUE_SUCCESS: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:31:52] QUEUE_SUCCESS: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:31:52] QUEUE_SUCCESS: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:31:52] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:31:53] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:31:53] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:31:53] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:31:53] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:31:53] QUEUE_SUCCESS: Bot #41 → '💯' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:31:54] QUEUE_SUCCESS: Bot #41 → '💯' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:31:54] QUEUE_SUCCESS: Bot #41 → '💯' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:31:54] QUEUE_SUCCESS: Bot #41 → '💯' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:31:54] QUEUE_SUCCESS: Bot #41 → '💯' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:31:56] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:31:56] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:31:56] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:31:56] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:31:56] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:31:57] QUEUE_SUCCESS: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:31:57] QUEUE_SUCCESS: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:31:57] QUEUE_SUCCESS: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:31:57] QUEUE_SUCCESS: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:31:57] QUEUE_ERROR: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:31:57] QUEUE_ERROR: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:31:57] QUEUE_SUCCESS: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:31:57] QUEUE_ERROR: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:31:57] QUEUE_ERROR: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:31:57] QUEUE_ERROR: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:31:57] QUEUE_ERROR: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:31:57] QUEUE_ERROR: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:31:57] QUEUE_ERROR: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:31:57] QUEUE_ERROR: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:31:57] QUEUE_ERROR: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:31:57] QUEUE_ERROR: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:31:57] QUEUE_ERROR: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:31:57] QUEUE_ERROR: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:31:57] QUEUE_ERROR: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:31:57] QUEUE_ERROR: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:31:57] QUEUE_ERROR: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:31:57] QUEUE_ERROR: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:31:57] QUEUE_ERROR: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:31:57] QUEUE_ERROR: Bot #40 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:32:00] QUEUE_SUCCESS: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:32:00] QUEUE_SUCCESS: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:32:00] QUEUE_SUCCESS: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:32:00] QUEUE_SUCCESS: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:32:00] QUEUE_SUCCESS: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:32:00] QUEUE_SUCCESS: Bot #44 → '💯' (Channel: -1002888471861, Message: 269, Delay: 860s)
[2025-08-07 07:32:00] QUEUE_SUMMARY: Processed 4 reactions, 2 errors, 2 remaining
[2025-08-07 07:32:01] QUEUE_SUCCESS: Bot #44 → '💯' (Channel: -1002888471861, Message: 269, Delay: 860s)
[2025-08-07 07:32:01] QUEUE_SUMMARY: Processed 4 reactions, 2 errors, 2 remaining
[2025-08-07 07:32:01] QUEUE_SUCCESS: Bot #44 → '💯' (Channel: -1002888471861, Message: 269, Delay: 860s)
[2025-08-07 07:32:01] QUEUE_SUMMARY: Processed 4 reactions, 2 errors, 2 remaining
[2025-08-07 07:32:01] QUEUE_SUCCESS: Bot #44 → '💯' (Channel: -1002888471861, Message: 269, Delay: 860s)
[2025-08-07 07:32:01] QUEUE_SUMMARY: Processed 4 reactions, 2 errors, 2 remaining
[2025-08-07 07:32:01] QUEUE_SUCCESS: Bot #44 → '💯' (Channel: -1002888471861, Message: 269, Delay: 860s)
[2025-08-07 07:32:01] QUEUE_SUMMARY: Processed 4 reactions, 2 errors, 2 remaining
[2025-08-07 07:32:04] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:32:04] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:32:04] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:32:04] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:32:04] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:32:04] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:32:04] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:32:04] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:32:04] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:32:04] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:32:04] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:32:04] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:32:04] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:32:04] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:32:04] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:32:04] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:32:04] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:32:04] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:32:04] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:32:04] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:32:04] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:32:04] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:32:04] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:32:04] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 3
[2025-08-07 07:32:05] QUEUE_SUCCESS: Bot #41 → '💯' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:32:05] QUEUE_SUCCESS: Bot #41 → '💯' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:32:05] QUEUE_SUCCESS: Bot #41 → '💯' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:32:05] QUEUE_SUCCESS: Bot #41 → '💯' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:32:05] QUEUE_SUCCESS: Bot #41 → '💯' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:32:05] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:32:06] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:32:06] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:32:06] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:32:06] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:32:06] QUEUE_SUCCESS: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:32:06] QUEUE_SUCCESS: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:32:06] QUEUE_SUCCESS: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:32:06] QUEUE_SUCCESS: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:32:06] QUEUE_SUCCESS: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:32:07] QUEUE_SUCCESS: Bot #44 → '💯' (Channel: -1002888471861, Message: 269, Delay: 860s)
[2025-08-07 07:32:07] QUEUE_SUMMARY: Processed 3 reactions, 3 errors, 2 remaining
[2025-08-07 07:32:07] QUEUE_SUCCESS: Bot #44 → '💯' (Channel: -1002888471861, Message: 269, Delay: 860s)
[2025-08-07 07:32:07] QUEUE_SUMMARY: Processed 3 reactions, 3 errors, 2 remaining
[2025-08-07 07:32:07] QUEUE_SUCCESS: Bot #44 → '💯' (Channel: -1002888471861, Message: 269, Delay: 860s)
[2025-08-07 07:32:07] QUEUE_SUMMARY: Processed 3 reactions, 3 errors, 2 remaining
[2025-08-07 07:32:07] QUEUE_SUCCESS: Bot #44 → '💯' (Channel: -1002888471861, Message: 269, Delay: 860s)
[2025-08-07 07:32:07] QUEUE_SUMMARY: Processed 3 reactions, 3 errors, 2 remaining
[2025-08-07 07:32:07] QUEUE_SUCCESS: Bot #44 → '💯' (Channel: -1002888471861, Message: 269, Delay: 860s)
[2025-08-07 07:32:07] QUEUE_SUMMARY: Processed 3 reactions, 3 errors, 2 remaining
[2025-08-07 07:32:09] QUEUE_ERROR: Bot #43 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:32:09] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 1 remaining
[2025-08-07 07:32:10] QUEUE_ERROR: Bot #43 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:32:10] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 1 remaining
[2025-08-07 07:32:10] QUEUE_ERROR: Bot #43 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:32:10] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 1 remaining
[2025-08-07 07:32:10] QUEUE_SUCCESS: Bot #41 → '💯' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:32:10] QUEUE_SUCCESS: Bot #41 → '💯' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:32:10] QUEUE_SUCCESS: Bot #41 → '💯' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:32:10] QUEUE_SUCCESS: Bot #41 → '💯' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:32:10] QUEUE_ERROR: Bot #41 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:32:10] QUEUE_ERROR: Bot #41 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:32:10] QUEUE_ERROR: Bot #41 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:32:10] QUEUE_ERROR: Bot #41 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:32:10] QUEUE_ERROR: Bot #41 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:32:10] QUEUE_SUCCESS: Bot #41 → '💯' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:32:10] QUEUE_ERROR: Bot #41 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:32:10] QUEUE_ERROR: Bot #41 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:32:10] QUEUE_ERROR: Bot #41 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:32:10] QUEUE_ERROR: Bot #41 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:32:10] QUEUE_ERROR: Bot #41 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:32:10] QUEUE_ERROR: Bot #41 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:32:10] QUEUE_ERROR: Bot #41 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:32:10] QUEUE_ERROR: Bot #41 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:32:10] QUEUE_ERROR: Bot #41 → '💯' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:32:11] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:32:11] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:32:11] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:32:11] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:32:11] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:32:11] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:32:11] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:32:11] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:32:11] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:32:11] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:32:11] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:32:11] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:32:11] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:32:11] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:32:11] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:32:11] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:32:11] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:32:11] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:32:11] QUEUE_ERROR: Bot #43 → '😘' (Channel: -1002888471861, Message: 269) - HTTP 401: Unauthorized
[2025-08-07 07:32:12] QUEUE_SUCCESS: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:32:12] QUEUE_SUCCESS: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:32:12] QUEUE_SUCCESS: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:32:12] QUEUE_SUCCESS: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:32:12] QUEUE_SUCCESS: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270, Delay: 823s)
[2025-08-07 07:32:12] QUEUE_ERROR: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 40
[2025-08-07 07:32:12] QUEUE_ERROR: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 40
[2025-08-07 07:32:12] QUEUE_ERROR: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 40
[2025-08-07 07:32:12] QUEUE_ERROR: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 40
[2025-08-07 07:32:12] QUEUE_ERROR: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 40
[2025-08-07 07:32:12] QUEUE_ERROR: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 40
[2025-08-07 07:32:12] QUEUE_ERROR: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 40
[2025-08-07 07:32:12] QUEUE_ERROR: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 40
[2025-08-07 07:32:12] QUEUE_ERROR: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 40
[2025-08-07 07:32:12] QUEUE_ERROR: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 40
[2025-08-07 07:32:12] QUEUE_ERROR: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 40
[2025-08-07 07:32:12] QUEUE_ERROR: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 40
[2025-08-07 07:32:12] QUEUE_ERROR: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 40
[2025-08-07 07:32:12] QUEUE_ERROR: Bot #42 → '❤️' (Channel: -1002888471861, Message: 270) - HTTP 429: Too Many Requests: retry after 40
[2025-08-07 07:32:12] QUEUE_SUCCESS: Bot #44 → '💯' (Channel: -1002888471861, Message: 269, Delay: 860s)
[2025-08-07 07:32:12] QUEUE_SUMMARY: Processed 3 reactions, 3 errors, 2 remaining
[2025-08-07 07:32:12] QUEUE_SUCCESS: Bot #44 → '💯' (Channel: -1002888471861, Message: 269, Delay: 860s)
[2025-08-07 07:32:12] QUEUE_SUMMARY: Processed 3 reactions, 3 errors, 2 remaining
[2025-08-07 07:32:13] QUEUE_SUCCESS: Bot #44 → '💯' (Channel: -1002888471861, Message: 269, Delay: 860s)
[2025-08-07 07:32:13] QUEUE_SUMMARY: Processed 3 reactions, 3 errors, 2 remaining
[2025-08-07 07:32:13] QUEUE_SUCCESS: Bot #44 → '💯' (Channel: -1002888471861, Message: 269, Delay: 860s)
[2025-08-07 07:32:13] QUEUE_SUMMARY: Processed 1 reactions, 5 errors, 2 remaining
[2025-08-07 07:32:13] QUEUE_SUCCESS: Bot #44 → '💯' (Channel: -1002888471861, Message: 269, Delay: 860s)
[2025-08-07 07:32:13] QUEUE_SUMMARY: Processed 2 reactions, 4 errors, 2 remaining
[2025-08-07 07:32:15] QUEUE_ERROR: Bot #43 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:32:15] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 1 remaining
[2025-08-07 07:32:15] QUEUE_ERROR: Bot #43 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:32:15] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 1 remaining
[2025-08-07 07:32:15] QUEUE_ERROR: Bot #43 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:32:15] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 1 remaining
[2025-08-07 07:32:16] QUEUE_ERROR: Bot #43 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:32:16] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 1 remaining
[2025-08-07 07:32:16] QUEUE_ERROR: Bot #43 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:32:16] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 1 remaining
[2025-08-07 07:32:18] QUEUE_SUCCESS: Bot #44 → '💯' (Channel: -1002888471861, Message: 269, Delay: 860s)
[2025-08-07 07:32:18] QUEUE_SUMMARY: Processed 2 reactions, 4 errors, 2 remaining
[2025-08-07 07:32:18] QUEUE_SUCCESS: Bot #44 → '💯' (Channel: -1002888471861, Message: 269, Delay: 860s)
[2025-08-07 07:32:18] QUEUE_SUMMARY: Processed 3 reactions, 3 errors, 2 remaining
[2025-08-07 07:32:18] QUEUE_SUCCESS: Bot #44 → '💯' (Channel: -1002888471861, Message: 269, Delay: 860s)
[2025-08-07 07:32:18] QUEUE_SUMMARY: Processed 2 reactions, 4 errors, 2 remaining
[2025-08-07 07:32:18] QUEUE_SUCCESS: Bot #44 → '💯' (Channel: -1002888471861, Message: 269, Delay: 860s)
[2025-08-07 07:32:18] QUEUE_SUMMARY: Processed 1 reactions, 5 errors, 2 remaining
[2025-08-07 07:32:18] QUEUE_SUCCESS: Bot #44 → '💯' (Channel: -1002888471861, Message: 269, Delay: 860s)
[2025-08-07 07:32:18] QUEUE_SUMMARY: Processed 1 reactions, 5 errors, 2 remaining
[2025-08-07 07:32:18] QUEUE_ERROR: Bot #44 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 42
[2025-08-07 07:32:18] QUEUE_SUMMARY: Processed 1 reactions, 5 errors, 2 remaining
[2025-08-07 07:32:18] QUEUE_ERROR: Bot #44 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 42
[2025-08-07 07:32:18] QUEUE_SUMMARY: Processed 1 reactions, 5 errors, 2 remaining
[2025-08-07 07:32:18] QUEUE_ERROR: Bot #44 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 42
[2025-08-07 07:32:18] QUEUE_SUMMARY: Processed 0 reactions, 6 errors, 2 remaining
[2025-08-07 07:32:18] QUEUE_ERROR: Bot #44 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 42
[2025-08-07 07:32:18] QUEUE_SUMMARY: Processed 1 reactions, 5 errors, 2 remaining
[2025-08-07 07:32:18] QUEUE_ERROR: Bot #44 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 42
[2025-08-07 07:32:18] QUEUE_SUMMARY: Processed 0 reactions, 6 errors, 2 remaining
[2025-08-07 07:32:18] QUEUE_ERROR: Bot #44 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 42
[2025-08-07 07:32:18] QUEUE_ERROR: Bot #44 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 42
[2025-08-07 07:32:18] QUEUE_SUMMARY: Processed 1 reactions, 5 errors, 2 remaining
[2025-08-07 07:32:18] QUEUE_SUMMARY: Processed 0 reactions, 6 errors, 2 remaining
[2025-08-07 07:32:18] QUEUE_ERROR: Bot #44 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 42
[2025-08-07 07:32:18] QUEUE_SUMMARY: Processed 0 reactions, 6 errors, 2 remaining
[2025-08-07 07:32:18] QUEUE_ERROR: Bot #44 → '💯' (Channel: -1002888471861, Message: 269) - HTTP 429: Too Many Requests: retry after 42
[2025-08-07 07:32:18] QUEUE_SUMMARY: Processed 0 reactions, 6 errors, 2 remaining
[2025-08-07 07:32:20] QUEUE_ERROR: Bot #43 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:32:20] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 1 remaining
[2025-08-07 07:32:20] QUEUE_ERROR: Bot #43 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:32:20] QUEUE_ERROR: Bot #43 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:32:20] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 1 remaining
[2025-08-07 07:32:20] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 1 remaining
[2025-08-07 07:32:20] QUEUE_ERROR: Bot #43 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:32:20] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 1 remaining
[2025-08-07 07:32:20] QUEUE_ERROR: Bot #43 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:32:20] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 1 remaining
[2025-08-07 07:32:20] QUEUE_ERROR: Bot #43 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:32:20] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 1 remaining
[2025-08-07 07:32:20] QUEUE_ERROR: Bot #43 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:32:20] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 1 remaining
[2025-08-07 07:32:20] QUEUE_ERROR: Bot #43 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:32:20] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 1 remaining
[2025-08-07 07:32:20] QUEUE_ERROR: Bot #43 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:32:20] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 1 remaining
[2025-08-07 07:32:20] QUEUE_ERROR: Bot #43 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:32:20] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 1 remaining
[2025-08-07 07:32:20] QUEUE_ERROR: Bot #43 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:32:20] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 1 remaining
[2025-08-07 07:32:20] QUEUE_ERROR: Bot #43 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:32:20] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 1 remaining
[2025-08-07 07:32:20] QUEUE_ERROR: Bot #43 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:32:20] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 1 remaining
[2025-08-07 07:32:21] QUEUE_ERROR: Bot #43 → '🤩' (Channel: -1002888471861, Message: 270) - HTTP 401: Unauthorized
[2025-08-07 07:32:21] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 1 remaining
[2025-08-07 07:33:06] QUEUE_SUCCESS: Bot #44 → '🤩' (Channel: -1002888471861, Message: 270, Delay: 897s)
[2025-08-07 07:33:06] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 0 remaining
[2025-08-07 07:33:06] POST_STATS: -1002888471861_270 - 1 successful, 0 errors
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #19 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #19 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #4 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #4 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #23 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #23 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #33 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #33 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #1 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_START: Bot #1 starting sequential processing for Channel: -1002888471861, Message: 271
[2025-08-07 07:40:49] QUEUE_CREATE_START: Bot #1 creating queue for Channel: -1002888471861, Message: 271
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #34 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #34 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #21 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #21 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #30 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #30 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #5 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #5 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] QUEUE_CREATE_SUCCESS: Added 43 reactions for Channel: -1002888471861, Message: 271, Duration: 860s
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #2 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #2 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #20 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #20 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #41 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #41 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #3 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #3 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #24 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #24 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #25 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #25 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #17 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #17 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #38 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #38 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #13 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #13 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #10 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #10 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #36 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #36 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #12 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #12 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #11 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #11 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #7 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #7 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #42 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #42 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #39 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #39 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #9 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #9 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #15 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #15 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #35 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #35 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #8 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #8 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #44 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #44 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #6 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #6 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #40 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #40 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #14 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #14 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:49] WEBHOOK_RECEIVED: Bot #16 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:49
[2025-08-07 07:40:49] SEQUENTIAL_SKIP: Bot #16 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:40:50] SEQUENTIAL_BOT1_SUCCESS: Bot #1 → '🥰' (Channel: -1002888471861, Message: 271) - IMMEDIATE
[2025-08-07 07:40:50] WEBHOOK_RECEIVED: Bot #37 received message from Channel: -1002888471861, Message: 271, Timestamp: 2025-08-07 07:40:50
[2025-08-07 07:40:50] SEQUENTIAL_SKIP: Bot #37 skipping immediate reaction for Channel: -1002888471861, Message: 271 (will process via queue)
[2025-08-07 07:41:51] QUEUE_SUCCESS: Bot #2 → '❤️' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:51] QUEUE_SUCCESS: Bot #2 → '❤️' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:51] QUEUE_SUCCESS: Bot #2 → '❤️' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:51] QUEUE_SUCCESS: Bot #2 → '❤️' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:51] QUEUE_SUCCESS: Bot #2 → '❤️' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:52] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:52] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:52] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:52] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:52] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:53] QUEUE_SUCCESS: Bot #4 → '🤩' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:53] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 40 remaining
[2025-08-07 07:41:53] QUEUE_SUCCESS: Bot #4 → '🤩' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:53] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 40 remaining
[2025-08-07 07:41:53] QUEUE_SUCCESS: Bot #4 → '🤩' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:53] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 40 remaining
[2025-08-07 07:41:53] QUEUE_SUCCESS: Bot #4 → '🤩' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:53] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 40 remaining
[2025-08-07 07:41:53] QUEUE_SUCCESS: Bot #4 → '🤩' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:53] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 40 remaining
[2025-08-07 07:41:56] QUEUE_SUCCESS: Bot #2 → '❤️' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:56] QUEUE_SUCCESS: Bot #2 → '❤️' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:56] QUEUE_SUCCESS: Bot #2 → '❤️' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:56] QUEUE_SUCCESS: Bot #2 → '❤️' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:56] QUEUE_SUCCESS: Bot #2 → '❤️' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:56] QUEUE_ERROR: Bot #2 → '❤️' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:41:56] QUEUE_ERROR: Bot #2 → '❤️' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:41:57] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:57] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:57] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:57] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:57] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:58] QUEUE_SUCCESS: Bot #4 → '🤩' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:58] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 40 remaining
[2025-08-07 07:41:58] QUEUE_SUCCESS: Bot #4 → '🤩' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:58] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 40 remaining
[2025-08-07 07:41:58] QUEUE_SUCCESS: Bot #4 → '🤩' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:58] QUEUE_SUMMARY: Processed 2 reactions, 1 errors, 40 remaining
[2025-08-07 07:41:58] QUEUE_SUCCESS: Bot #4 → '🤩' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:58] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 40 remaining
[2025-08-07 07:41:58] QUEUE_SUCCESS: Bot #4 → '🤩' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:41:58] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 40 remaining
[2025-08-07 07:42:02] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:42:02] QUEUE_SUCCESS: Bot #3 → '😘' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:42:03] QUEUE_SUCCESS: Bot #4 → '🤩' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:42:03] QUEUE_SUMMARY: Processed 2 reactions, 1 errors, 40 remaining
[2025-08-07 07:42:03] QUEUE_SUCCESS: Bot #4 → '🤩' (Channel: -1002888471861, Message: 271, Delay: 61s)
[2025-08-07 07:42:03] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 40 remaining
[2025-08-07 07:42:16] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:42:16] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:17] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:42:17] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:17] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:42:17] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:17] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:42:17] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:17] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:42:17] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:22] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:42:22] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:22] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:42:22] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:22] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:42:22] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:22] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:42:22] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:22] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:42:22] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:26] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP Unknown: No response
[2025-08-07 07:42:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:26] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP Unknown: No response
[2025-08-07 07:42:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:26] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP Unknown: No response
[2025-08-07 07:42:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:26] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP Unknown: No response
[2025-08-07 07:42:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:26] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP Unknown: No response
[2025-08-07 07:42:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:26] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP Unknown: No response
[2025-08-07 07:42:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:26] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP Unknown: No response
[2025-08-07 07:42:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:26] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP Unknown: No response
[2025-08-07 07:42:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:26] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP Unknown: No response
[2025-08-07 07:42:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:26] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP Unknown: No response
[2025-08-07 07:42:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:26] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP Unknown: No response
[2025-08-07 07:42:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:26] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP Unknown: No response
[2025-08-07 07:42:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:26] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP Unknown: No response
[2025-08-07 07:42:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:26] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP Unknown: No response
[2025-08-07 07:42:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:26] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP Unknown: No response
[2025-08-07 07:42:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:26] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP Unknown: No response
[2025-08-07 07:42:26] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP Unknown: No response
[2025-08-07 07:42:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:26] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP Unknown: No response
[2025-08-07 07:42:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:26] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP Unknown: No response
[2025-08-07 07:42:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:26] QUEUE_ERROR: Bot #5 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP Unknown: No response
[2025-08-07 07:42:26] QUEUE_SUMMARY: Processed 0 reactions, 1 errors, 39 remaining
[2025-08-07 07:42:57] QUEUE_SUCCESS: Bot #6 → '😘' (Channel: -1002888471861, Message: 271, Delay: 127s)
[2025-08-07 07:42:57] QUEUE_SUCCESS: Bot #6 → '😘' (Channel: -1002888471861, Message: 271, Delay: 127s)
[2025-08-07 07:42:57] QUEUE_SUCCESS: Bot #6 → '😘' (Channel: -1002888471861, Message: 271, Delay: 127s)
[2025-08-07 07:42:57] QUEUE_SUCCESS: Bot #6 → '😘' (Channel: -1002888471861, Message: 271, Delay: 127s)
[2025-08-07 07:42:57] QUEUE_SUCCESS: Bot #6 → '😘' (Channel: -1002888471861, Message: 271, Delay: 127s)
[2025-08-07 07:42:58] QUEUE_SUCCESS: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271, Delay: 127s)
[2025-08-07 07:42:58] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 37 remaining
[2025-08-07 07:42:58] QUEUE_SUCCESS: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271, Delay: 127s)
[2025-08-07 07:42:58] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 37 remaining
[2025-08-07 07:42:58] QUEUE_SUCCESS: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271, Delay: 127s)
[2025-08-07 07:42:58] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 37 remaining
[2025-08-07 07:42:58] QUEUE_SUCCESS: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271, Delay: 127s)
[2025-08-07 07:42:58] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 37 remaining
[2025-08-07 07:42:58] QUEUE_SUCCESS: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271, Delay: 127s)
[2025-08-07 07:42:58] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 37 remaining
[2025-08-07 07:43:02] QUEUE_SUCCESS: Bot #6 → '😘' (Channel: -1002888471861, Message: 271, Delay: 127s)
[2025-08-07 07:43:02] QUEUE_SUCCESS: Bot #6 → '😘' (Channel: -1002888471861, Message: 271, Delay: 127s)
[2025-08-07 07:43:02] QUEUE_SUCCESS: Bot #6 → '😘' (Channel: -1002888471861, Message: 271, Delay: 127s)
[2025-08-07 07:43:02] QUEUE_SUCCESS: Bot #6 → '😘' (Channel: -1002888471861, Message: 271, Delay: 127s)
[2025-08-07 07:43:02] QUEUE_ERROR: Bot #6 → '😘' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:02] QUEUE_SUCCESS: Bot #6 → '😘' (Channel: -1002888471861, Message: 271, Delay: 127s)
[2025-08-07 07:43:02] QUEUE_ERROR: Bot #6 → '😘' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:02] QUEUE_ERROR: Bot #6 → '😘' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:02] QUEUE_ERROR: Bot #6 → '😘' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:02] QUEUE_ERROR: Bot #6 → '😘' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:02] QUEUE_ERROR: Bot #6 → '😘' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:02] QUEUE_ERROR: Bot #6 → '😘' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:02] QUEUE_ERROR: Bot #6 → '😘' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:02] QUEUE_ERROR: Bot #6 → '😘' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:02] QUEUE_ERROR: Bot #6 → '😘' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:02] QUEUE_ERROR: Bot #6 → '😘' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:02] QUEUE_ERROR: Bot #6 → '😘' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:02] QUEUE_ERROR: Bot #6 → '😘' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:02] QUEUE_ERROR: Bot #6 → '😘' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:02] QUEUE_ERROR: Bot #6 → '😘' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:02] QUEUE_ERROR: Bot #6 → '😘' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:02] QUEUE_ERROR: Bot #6 → '😘' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:02] QUEUE_ERROR: Bot #6 → '😘' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:02] QUEUE_ERROR: Bot #6 → '😘' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:02] QUEUE_ERROR: Bot #6 → '😘' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:03] QUEUE_SUCCESS: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271, Delay: 127s)
[2025-08-07 07:43:03] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 37 remaining
[2025-08-07 07:43:03] QUEUE_SUCCESS: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271, Delay: 127s)
[2025-08-07 07:43:03] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 37 remaining
[2025-08-07 07:43:03] QUEUE_SUCCESS: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271, Delay: 127s)
[2025-08-07 07:43:03] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 37 remaining
[2025-08-07 07:43:03] QUEUE_SUCCESS: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271, Delay: 127s)
[2025-08-07 07:43:03] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 37 remaining
[2025-08-07 07:43:03] QUEUE_SUCCESS: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271, Delay: 127s)
[2025-08-07 07:43:03] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 37 remaining
[2025-08-07 07:43:08] QUEUE_SUCCESS: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271, Delay: 127s)
[2025-08-07 07:43:08] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 37 remaining
[2025-08-07 07:43:08] QUEUE_SUCCESS: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271, Delay: 127s)
[2025-08-07 07:43:08] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 37 remaining
[2025-08-07 07:43:08] QUEUE_SUCCESS: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271, Delay: 127s)
[2025-08-07 07:43:08] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 37 remaining
[2025-08-07 07:43:08] QUEUE_SUCCESS: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271, Delay: 127s)
[2025-08-07 07:43:08] QUEUE_SUMMARY: Processed 2 reactions, 0 errors, 37 remaining
[2025-08-07 07:43:08] QUEUE_SUCCESS: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271, Delay: 127s)
[2025-08-07 07:43:08] QUEUE_SUMMARY: Processed 1 reactions, 1 errors, 37 remaining
[2025-08-07 07:43:08] QUEUE_ERROR: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:08] QUEUE_ERROR: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:08] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 37 remaining
[2025-08-07 07:43:08] QUEUE_ERROR: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:08] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 37 remaining
[2025-08-07 07:43:08] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 37 remaining
[2025-08-07 07:43:08] QUEUE_ERROR: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:08] QUEUE_ERROR: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:08] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 37 remaining
[2025-08-07 07:43:08] QUEUE_ERROR: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:08] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 37 remaining
[2025-08-07 07:43:08] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 37 remaining
[2025-08-07 07:43:08] QUEUE_ERROR: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:08] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 37 remaining
[2025-08-07 07:43:08] QUEUE_ERROR: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:08] QUEUE_ERROR: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:08] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 37 remaining
[2025-08-07 07:43:08] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 37 remaining
[2025-08-07 07:43:08] QUEUE_ERROR: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:08] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 37 remaining
[2025-08-07 07:43:08] QUEUE_ERROR: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:08] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 37 remaining
[2025-08-07 07:43:08] QUEUE_ERROR: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:08] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 37 remaining
[2025-08-07 07:43:08] QUEUE_ERROR: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:08] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 37 remaining
[2025-08-07 07:43:08] QUEUE_ERROR: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:08] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 37 remaining
[2025-08-07 07:43:08] QUEUE_ERROR: Bot #7 → '🏆' (Channel: -1002888471861, Message: 271) - HTTP 429: Too Many Requests: retry after 5
[2025-08-07 07:43:08] QUEUE_SUMMARY: Processed 0 reactions, 2 errors, 37 remaining
[2025-08-07 07:44:04] QUEUE_SUCCESS: Bot #8 → '🥰' (Channel: -1002888471861, Message: 271, Delay: 195s)
[2025-08-07 07:44:05] QUEUE_SUCCESS: Bot #9 → '🥰' (Channel: -1002888471861, Message: 271, Delay: 195s)
[2025-08-07 07:44:06] QUEUE_SUCCESS: Bot #10 → '💯' (Channel: -1002888471861, Message: 271, Delay: 195s)
[2025-08-07 07:44:06] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 34 remaining
[2025-08-07 07:44:06] POST_STATS: -1002888471861_271 - 3 successful, 0 errors
[2025-08-07 07:45:06] QUEUE_SUCCESS: Bot #11 → '🥰' (Channel: -1002888471861, Message: 271, Delay: 256s)
[2025-08-07 07:45:06] QUEUE_SUCCESS: Bot #12 → '💯' (Channel: -1002888471861, Message: 271, Delay: 256s)
[2025-08-07 07:45:07] QUEUE_SUCCESS: Bot #13 → '🤩' (Channel: -1002888471861, Message: 271, Delay: 256s)
[2025-08-07 07:45:07] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 31 remaining
[2025-08-07 07:45:07] POST_STATS: -1002888471861_271 - 3 successful, 0 errors
[2025-08-07 07:46:05] QUEUE_SUCCESS: Bot #14 → '🤩' (Channel: -1002888471861, Message: 271, Delay: 315s)
[2025-08-07 07:46:06] QUEUE_SUCCESS: Bot #15 → '💯' (Channel: -1002888471861, Message: 271, Delay: 315s)
[2025-08-07 07:46:07] QUEUE_ERROR: Bot #16 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:46:07] QUEUE_SUMMARY: Processed 2 reactions, 1 errors, 28 remaining
[2025-08-07 07:46:07] POST_STATS: -1002888471861_271 - 2 successful, 1 errors
[2025-08-07 07:47:04] QUEUE_SUCCESS: Bot #17 → '😘' (Channel: -1002888471861, Message: 271, Delay: 374s)
[2025-08-07 07:47:05] QUEUE_ERROR: Bot #18 → '❤️' (Channel: -1002888471861, Message: 271) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:47:06] QUEUE_SUCCESS: Bot #19 → '❤️' (Channel: -1002888471861, Message: 271, Delay: 374s)
[2025-08-07 07:47:06] QUEUE_SUMMARY: Processed 2 reactions, 1 errors, 25 remaining
[2025-08-07 07:47:06] POST_STATS: -1002888471861_271 - 2 successful, 1 errors
[2025-08-07 07:48:05] QUEUE_SUCCESS: Bot #20 → '❤️' (Channel: -1002888471861, Message: 271, Delay: 436s)
[2025-08-07 07:48:06] QUEUE_SUCCESS: Bot #21 → '💯' (Channel: -1002888471861, Message: 271, Delay: 436s)
[2025-08-07 07:48:07] QUEUE_ERROR: Bot #22 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:48:07] QUEUE_SUMMARY: Processed 2 reactions, 1 errors, 22 remaining
[2025-08-07 07:48:07] POST_STATS: -1002888471861_271 - 2 successful, 1 errors
[2025-08-07 07:49:05] QUEUE_SUCCESS: Bot #23 → '🥰' (Channel: -1002888471861, Message: 271, Delay: 495s)
[2025-08-07 07:49:06] QUEUE_SUCCESS: Bot #24 → '🏆' (Channel: -1002888471861, Message: 271, Delay: 495s)
[2025-08-07 07:49:07] QUEUE_SUCCESS: Bot #25 → '😘' (Channel: -1002888471861, Message: 271, Delay: 495s)
[2025-08-07 07:49:07] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 19 remaining
[2025-08-07 07:49:07] POST_STATS: -1002888471861_271 - 3 successful, 0 errors
[2025-08-07 07:50:06] QUEUE_ERROR: Bot #26 → '🥰' (Channel: -1002888471861, Message: 271) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:50:07] QUEUE_ERROR: Bot #27 → '🥰' (Channel: -1002888471861, Message: 271) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:50:08] QUEUE_ERROR: Bot #28 → '❤️' (Channel: -1002888471861, Message: 271) - HTTP 401: Unauthorized
[2025-08-07 07:50:08] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 16 remaining
[2025-08-07 07:50:08] POST_STATS: -1002888471861_271 - 0 successful, 3 errors
[2025-08-07 07:51:04] QUEUE_ERROR: Bot #29 → '🥰' (Channel: -1002888471861, Message: 271) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:51:05] QUEUE_SUCCESS: Bot #30 → '😘' (Channel: -1002888471861, Message: 271, Delay: 614s)
[2025-08-07 07:51:06] QUEUE_ERROR: Bot #31 → '🤩' (Channel: -1002888471861, Message: 271) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:51:06] QUEUE_SUMMARY: Processed 1 reactions, 2 errors, 13 remaining
[2025-08-07 07:51:06] POST_STATS: -1002888471861_271 - 1 successful, 2 errors
[2025-08-07 07:52:04] QUEUE_ERROR: Bot #32 → '❤️' (Channel: -1002888471861, Message: 271) - HTTP 400: Bad Request: chat not found
[2025-08-07 07:52:05] QUEUE_ERROR: Bot #33 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:52:06] QUEUE_ERROR: Bot #34 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:52:06] QUEUE_SUMMARY: Processed 0 reactions, 3 errors, 10 remaining
[2025-08-07 07:52:06] POST_STATS: -1002888471861_271 - 0 successful, 3 errors
[2025-08-07 07:53:05] QUEUE_SUCCESS: Bot #35 → '🤩' (Channel: -1002888471861, Message: 271, Delay: 735s)
[2025-08-07 07:53:06] QUEUE_SUCCESS: Bot #36 → '🏆' (Channel: -1002888471861, Message: 271, Delay: 735s)
[2025-08-07 07:53:07] QUEUE_SUCCESS: Bot #37 → '❤️' (Channel: -1002888471861, Message: 271, Delay: 735s)
[2025-08-07 07:53:07] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 7 remaining
[2025-08-07 07:53:07] POST_STATS: -1002888471861_271 - 3 successful, 0 errors
[2025-08-07 07:54:05] QUEUE_SUCCESS: Bot #38 → '❤️' (Channel: -1002888471861, Message: 271, Delay: 795s)
[2025-08-07 07:54:06] QUEUE_SUCCESS: Bot #39 → '🤩' (Channel: -1002888471861, Message: 271, Delay: 795s)
[2025-08-07 07:54:07] QUEUE_SUCCESS: Bot #40 → '🥰' (Channel: -1002888471861, Message: 271, Delay: 795s)
[2025-08-07 07:54:07] QUEUE_SUMMARY: Processed 3 reactions, 0 errors, 4 remaining
[2025-08-07 07:54:07] POST_STATS: -1002888471861_271 - 3 successful, 0 errors
[2025-08-07 07:55:05] QUEUE_SUCCESS: Bot #41 → '❤️' (Channel: -1002888471861, Message: 271, Delay: 855s)
[2025-08-07 07:55:06] QUEUE_ERROR: Bot #42 → '🎉' (Channel: -1002888471861, Message: 271) - HTTP 400: Bad Request: REACTION_INVALID
[2025-08-07 07:55:07] QUEUE_ERROR: Bot #43 → '🤩' (Channel: -1002888471861, Message: 271) - HTTP 401: Unauthorized
[2025-08-07 07:55:07] QUEUE_SUMMARY: Processed 1 reactions, 2 errors, 1 remaining
[2025-08-07 07:55:07] POST_STATS: -1002888471861_271 - 1 successful, 2 errors
[2025-08-07 07:56:05] QUEUE_SUCCESS: Bot #44 → '🏆' (Channel: -1002888471861, Message: 271, Delay: 916s)
[2025-08-07 07:56:05] QUEUE_SUMMARY: Processed 1 reactions, 0 errors, 0 remaining
[2025-08-07 07:56:05] POST_STATS: -1002888471861_271 - 1 successful, 0 errors
